/**
 * Integration Test for SahAI V2 Enhanced Model Loading
 * Tests the proxy server and enhanced API service functionality
 */

const fetch = require('node-fetch');

const PROXY_URL = 'http://localhost:3131';

async function testProxyHealth() {
  console.log('🔍 Testing proxy health...');
  try {
    const response = await fetch(`${PROXY_URL}/health`);
    const data = await response.json();
    console.log('✅ Proxy health check passed:', data);
    return true;
  } catch (error) {
    console.error('❌ Proxy health check failed:', error.message);
    return false;
  }
}

async function testModelFetching(providerId) {
  console.log(`🔍 Testing model fetching for ${providerId}...`);
  try {
    const response = await fetch(`${PROXY_URL}/api/models/${providerId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ forceRefresh: false }),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    console.log(`✅ ${providerId} models fetched successfully:`, {
      provider: data.provider,
      modelCount: data.models.length,
      cached: data.cached,
      sampleModels: data.models.slice(0, 3).map(m => ({ id: m.id, name: m.name }))
    });
    return data.models.length > 0;
  } catch (error) {
    console.error(`❌ Model fetching failed for ${providerId}:`, error.message);
    return false;
  }
}

async function testCaching(providerId) {
  console.log(`🔍 Testing caching for ${providerId}...`);
  
  // First request (should not be cached)
  const start1 = Date.now();
  const response1 = await fetch(`${PROXY_URL}/api/models/${providerId}`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ forceRefresh: true }),
  });
  const data1 = await response1.json();
  const time1 = Date.now() - start1;

  // Second request (should be cached)
  const start2 = Date.now();
  const response2 = await fetch(`${PROXY_URL}/api/models/${providerId}`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ forceRefresh: false }),
  });
  const data2 = await response2.json();
  const time2 = Date.now() - start2;

  console.log(`✅ Caching test results for ${providerId}:`, {
    firstRequest: { cached: data1.cached, time: `${time1}ms`, models: data1.models.length },
    secondRequest: { cached: data2.cached, time: `${time2}ms`, models: data2.models.length },
    speedImprovement: `${Math.round((time1 - time2) / time1 * 100)}%`
  });

  return data2.cached && time2 < time1;
}

async function runIntegrationTests() {
  console.log('🚀 Starting SahAI V2 Enhanced Model Loading Integration Tests');
  console.log('='.repeat(60));

  const results = {
    healthCheck: false,
    modelFetching: {},
    caching: {}
  };

  // Test 1: Health Check
  results.healthCheck = await testProxyHealth();
  console.log('');

  if (!results.healthCheck) {
    console.error('❌ Proxy server is not healthy. Aborting tests.');
    return;
  }

  // Test 2: Model Fetching for Different Providers
  const providers = ['openrouter', 'groq', 'together'];
  
  for (const provider of providers) {
    results.modelFetching[provider] = await testModelFetching(provider);
    console.log('');
    
    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  // Test 3: Caching Performance
  console.log('🔍 Testing caching performance...');
  for (const provider of providers.slice(0, 2)) { // Test caching for first 2 providers
    results.caching[provider] = await testCaching(provider);
    console.log('');
    
    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  // Summary
  console.log('📊 Test Results Summary');
  console.log('='.repeat(30));
  console.log(`Health Check: ${results.healthCheck ? '✅ PASS' : '❌ FAIL'}`);
  
  console.log('\nModel Fetching:');
  Object.entries(results.modelFetching).forEach(([provider, success]) => {
    console.log(`  ${provider}: ${success ? '✅ PASS' : '❌ FAIL'}`);
  });

  console.log('\nCaching:');
  Object.entries(results.caching).forEach(([provider, success]) => {
    console.log(`  ${provider}: ${success ? '✅ PASS' : '❌ FAIL'}`);
  });

  const totalTests = 1 + Object.keys(results.modelFetching).length + Object.keys(results.caching).length;
  const passedTests = (results.healthCheck ? 1 : 0) + 
                     Object.values(results.modelFetching).filter(Boolean).length +
                     Object.values(results.caching).filter(Boolean).length;

  console.log(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed (${Math.round(passedTests/totalTests*100)}%)`);

  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! SahAI V2 Enhanced Model Loading is working correctly.');
  } else {
    console.log('⚠️  Some tests failed. Please check the implementation.');
  }
}

// Run the tests
if (require.main === module) {
  runIntegrationTests().catch(console.error);
}

module.exports = { runIntegrationTests };
