{"name": "sahai-cep-extension", "version": "1.1.0", "description": "SahAI CEP Chat Bot Extension for Adobe Creative Suite", "main": "client/index.html", "scripts": {"dev": "vite", "build": "NODE_ENV=production vite build && node scripts/copy-cep-files.js", "build:dev": "vite build --mode development && node scripts/copy-cep-files.js", "build:prod": "NODE_ENV=production DISABLE_SOURCEMAP=true vite build && node scripts/copy-cep-files.js", "build:csp": "npm run build && node scripts/generate-csp-hash.js --output ./dist/index.html", "preview": "vite preview", "package": "node scripts/package.js", "clean": "<PERSON><PERSON><PERSON> dist build", "type-check": "tsc --noEmit", "security:audit": "npm audit --audit-level moderate", "security:fix": "npm audit fix", "csp:generate": "node scripts/generate-csp-hash.js", "csp:restore": "node scripts/generate-csp-hash.js --restore", "copy:cep": "node scripts/copy-cep-files.js", "enable-debug": "node scripts/enable-debug-cross-platform.js", "cep:package": "./scripts/package-extension.sh", "cep:cert": "./scripts/create-self-signed-cert.sh", "cep:install": "./scripts/install-extension.sh", "cep:fix-cc2025": "./scripts/fix-cc2025-signature-issue.sh", "deploy": "npm run build && npm run package && npm run cep:install", "deploy:dev": "npm run build:dev && npm run package && npm run cep:install"}, "keywords": ["adobe", "cep", "extension", "ai", "chat", "creative-suite"], "author": "SahAI", "license": "MIT", "devDependencies": {"@types/node": "^20.10.0", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@typescript-eslint/eslint-plugin": "^8.37.0", "@vitejs/plugin-react": "^4.6.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-unused-imports": "^4.1.4", "fs-extra": "^11.3.0", "rimraf": "^5.0.5", "sass": "^1.69.5", "terser": "^5.24.0", "typescript": "^5.3.3", "vite": "^7.0.2", "vite-plugin-static-copy": "^3.1.0"}, "dependencies": {"@lobehub/icons": "^2.10.0", "axios": "^1.6.2", "compression": "^1.8.1", "cors": "^2.8.5", "express": "^5.1.0", "helmet": "^8.1.0", "node-fetch": "^2.7.0", "react": "^19.0.0", "react-dom": "^19.0.0", "zustand": "^4.4.7"}}