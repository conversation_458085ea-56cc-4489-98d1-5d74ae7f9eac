/**
 * Enhanced API Service for SahAI V2 with Proxy Integration
 * Extends the existing API service to work with the proxy server while maintaining backward compatibility
 */

import { ProxyLauncher } from '../proxy/proxyLauncher';
import { ModelInfo } from '../../stores/settingsStore';
import { ApiService } from './apiService';
import { AuthConfig, ApiError } from '../../types/api';

export interface ProxyModelResponse {
  provider: string;
  models: ModelInfo[];
  cached: boolean;
  timestamp: string;
}

export class EnhancedApiService extends ApiService {
  private proxyLauncher: ProxyLauncher;
  private cache = new Map<string, { data: ModelInfo[]; timestamp: number }>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  constructor() {
    super();
    this.proxyLauncher = ProxyLauncher.getInstance();
  }

  /**
   * Initialize the enhanced API service (launches proxy)
   */
  async initialize(): Promise<boolean> {
    console.log('🔄 Initializing Enhanced API service...');
    const success = await this.proxyLauncher.launchProxy();
    if (success) {
      console.log('✅ Enhanced API service initialized successfully');
      // Test connectivity
      const testResult = await this.proxyLauncher.testProxyConnectivity();
      if (testResult) {
        console.log('✅ Proxy connectivity test passed');
      } else {
        console.warn('⚠️ Proxy connectivity test failed, but proxy is running');
      }
    } else {
      console.error('❌ Failed to initialize Enhanced API service');
    }
    return success;
  }

  /**
   * Fetch models for a provider using hybrid proxy/direct approach
   */
  async fetchModels(providerId: string, apiKey?: string, forceRefresh = false): Promise<ModelInfo[]> {
    console.log(`🔄 Fetching models for provider: ${providerId}`);

    // Check cache first (unless force refresh)
    if (!forceRefresh) {
      const cached = this.getFromCache(providerId);
      if (cached) {
        console.log(`📦 Cache hit for ${providerId}: ${cached.length} models`);
        return cached;
      }
    }

    // Try proxy approach first
    if (this.proxyLauncher.isProxyRunning()) {
      try {
        const models = await this.fetchModelsViaProxy(providerId, apiKey, forceRefresh);
        if (models.length > 0) {
          this.setCache(providerId, models);
          console.log(`✅ Proxy fetch successful for ${providerId}: ${models.length} models`);
          return models;
        }
      } catch (error) {
        console.warn(`⚠️ Proxy fetch failed for ${providerId}:`, error);
      }
    }

    // Fallback to direct API approach (existing functionality)
    try {
      const models = await this.fetchModelsDirectly(providerId, apiKey);
      if (models.length > 0) {
        this.setCache(providerId, models);
        console.log(`✅ Direct fetch successful for ${providerId}: ${models.length} models`);
        return models;
      }
    } catch (error) {
      console.warn(`⚠️ Direct fetch failed for ${providerId}:`, error);
    }

    // Final fallback to cached data (even if stale)
    const staleCache = this.getFromCache(providerId, true);
    if (staleCache) {
      console.log(`📦 Using stale cache for ${providerId}: ${staleCache.length} models`);
      return staleCache;
    }

    // Final fallback - return empty array with clear error
    console.log(`❌ No models available for ${providerId} - all fetch methods failed`);
    return [];
  }

  /**
   * Fetch models via proxy server
   */
  private async fetchModelsViaProxy(providerId: string, apiKey?: string, forceRefresh = false): Promise<ModelInfo[]> {
    const proxyUrl = this.proxyLauncher.getProxyUrl();
    
    const response = await fetch(`${proxyUrl}/api/models/${providerId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ apiKey, forceRefresh }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
    }

    const data: ProxyModelResponse = await response.json();
    return data.models || [];
  }

  /**
   * Fetch models directly using existing adapter system
   */
  private async fetchModelsDirectly(providerId: string, apiKey?: string): Promise<ModelInfo[]> {
    const adapter = this.getAdapter(providerId);
    if (!adapter) {
      console.warn(`⚠️ No adapter found for provider: ${providerId}`);
      return [];
    }

    try {
      const authConfig: AuthConfig = {
        type: 'bearer',
        ...(apiKey && { token: apiKey, apiKey: apiKey })
      };

      // Use existing adapter system
      const models = await this.discoverModels(providerId, authConfig);
      return models;
    } catch (error) {
      console.warn(`⚠️ Adapter-based fetch failed for ${providerId}:`, error);
      return [];
    }
  }

  /**
   * Batch fetch models for multiple providers
   */
  async fetchMultipleProviders(providerConfigs: Array<{providerId: string, apiKey?: string}>): Promise<Record<string, ModelInfo[]>> {
    const results: Record<string, ModelInfo[]> = {};

    // Fetch in parallel with some delay to avoid overwhelming APIs
    const promises = providerConfigs.map(async (config, index) => {
      // Stagger requests by 500ms each
      await new Promise(resolve => setTimeout(resolve, index * 500));

      try {
        const models = await this.fetchModels(config.providerId, config.apiKey);
        results[config.providerId] = models;
      } catch (error) {
        console.error(`Failed to fetch models for ${config.providerId}:`, error);
        results[config.providerId] = [];
      }
    });

    await Promise.allSettled(promises);
    return results;
  }

  /**
   * Check if proxy server is healthy
   */
  async checkProxyHealth(): Promise<boolean> {
    return this.proxyLauncher.checkProxyHealth();
  }

  /**
   * Get proxy status information
   */
  getProxyStatus(): { isRunning: boolean; url: string } {
    return {
      isRunning: this.proxyLauncher.isProxyRunning(),
      url: this.proxyLauncher.getProxyUrl()
    };
  }

  // Cache management
  private getFromCache(providerId: string, allowStale = false): ModelInfo[] | null {
    const entry = this.cache.get(providerId);
    if (!entry) return null;

    const isStale = Date.now() - entry.timestamp > this.CACHE_TTL;
    if (isStale && !allowStale) return null;

    return entry.data;
  }

  private setCache(providerId: string, models: ModelInfo[]): void {
    this.cache.set(providerId, {
      data: models,
      timestamp: Date.now()
    });
  }



  /**
   * Cleanup resources
   */
  shutdown(): void {
    this.proxyLauncher.shutdown();
    this.cache.clear();
    console.log('🔄 Enhanced API service shutdown');
  }
}

// Create singleton instance
export const enhancedApiService = new EnhancedApiService();
