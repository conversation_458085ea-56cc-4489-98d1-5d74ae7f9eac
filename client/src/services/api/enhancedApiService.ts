/**
 * Enhanced API Service for SahAI V2 with Proxy Integration
 * Extends the existing API service to work with the proxy server while maintaining backward compatibility
 */

import { ProxyLauncher } from '../proxy/proxyLauncher';
import { ModelInfo } from '../../stores/settingsStore';
import { ApiService } from './apiService';
import { AuthConfig, ApiError } from '../../types/api';

export interface ProxyModelResponse {
  provider: string;
  models: ModelInfo[];
  cached: boolean;
  timestamp: string;
}

export class EnhancedApiService extends ApiService {
  private proxyLauncher: ProxyLauncher;
  private cache = new Map<string, { data: ModelInfo[]; timestamp: number }>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  constructor() {
    super();
    this.proxyLauncher = ProxyLauncher.getInstance();
  }

  /**
   * Initialize the enhanced API service (launches proxy)
   */
  async initialize(): Promise<boolean> {
    console.log('🔄 Initializing Enhanced API service...');
    const success = await this.proxyLauncher.launchProxy();
    if (success) {
      console.log('✅ Enhanced API service initialized successfully');
      // Test connectivity
      const testResult = await this.proxyLauncher.testProxyConnectivity();
      if (testResult) {
        console.log('✅ Proxy connectivity test passed');
      } else {
        console.warn('⚠️ Proxy connectivity test failed, but proxy is running');
      }
    } else {
      console.error('❌ Failed to initialize Enhanced API service');
    }
    return success;
  }

  /**
   * Lazy load models only when provider is selected (Cline-style optimization)
   */
  async lazyLoadModels(providerId: string, apiKey?: string): Promise<ModelInfo[]> {
    // For local providers (Ollama, LM Studio), only fetch when explicitly requested
    if (providerId === 'ollama' || providerId === 'lmstudio') {
      console.log(`🔄 Lazy loading local models for ${providerId}`);
      return this.fetchModels(providerId, apiKey, false);
    }

    // For online providers, check cache first and only fetch if needed
    const cached = this.getFromCache(providerId);
    if (cached && cached.length > 0) {
      console.log(`📦 Using cached models for ${providerId}: ${cached.length} models`);
      return cached;
    }

    // If no cache, fetch models
    console.log(`🔄 Lazy loading models for ${providerId}`);
    return this.fetchModels(providerId, apiKey, false);
  }

  /**
   * Fetch models for a provider using hybrid proxy/direct approach
   */
  async fetchModels(providerId: string, apiKey?: string, forceRefresh = false): Promise<ModelInfo[]> {
    console.log(`🔄 Fetching models for provider: ${providerId}`);

    // Check cache first (unless force refresh)
    if (!forceRefresh) {
      const cached = this.getFromCache(providerId);
      if (cached) {
        console.log(`📦 Cache hit for ${providerId}: ${cached.length} models`);
        return cached;
      }
    }

    // Try proxy approach first
    if (this.proxyLauncher.isProxyRunning()) {
      try {
        const models = await this.fetchModelsViaProxy(providerId, apiKey, forceRefresh);
        if (models.length > 0) {
          this.setCache(providerId, models);
          console.log(`✅ Proxy fetch successful for ${providerId}: ${models.length} models`);
          return models;
        }
      } catch (error) {
        console.warn(`⚠️ Proxy fetch failed for ${providerId}:`, error);
      }
    }

    // Fallback to direct API approach (existing functionality)
    try {
      const models = await this.fetchModelsDirectly(providerId, apiKey);
      if (models.length > 0) {
        this.setCache(providerId, models);
        console.log(`✅ Direct fetch successful for ${providerId}: ${models.length} models`);
        return models;
      }
    } catch (error) {
      console.warn(`⚠️ Direct fetch failed for ${providerId}:`, error);
    }

    // Final fallback to cached data (even if stale)
    const staleCache = this.getFromCache(providerId, true);
    if (staleCache) {
      console.log(`📦 Using stale cache for ${providerId}: ${staleCache.length} models`);
      return staleCache;
    }

    // Final fallback - return empty array with clear error
    console.log(`❌ No models available for ${providerId} - all fetch methods failed`);
    return [];
  }

  /**
   * Fetch models via proxy server
   */
  private async fetchModelsViaProxy(providerId: string, apiKey?: string, forceRefresh = false): Promise<ModelInfo[]> {
    const proxyUrl = this.proxyLauncher.getProxyUrl();
    
    const response = await fetch(`${proxyUrl}/api/models/${providerId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ apiKey, forceRefresh }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
    }

    const data: ProxyModelResponse = await response.json();
    return data.models || [];
  }

  /**
   * Fetch models directly using existing adapter system
   */
  private async fetchModelsDirectly(providerId: string, apiKey?: string): Promise<ModelInfo[]> {
    const adapter = this.getAdapter(providerId);
    if (!adapter) {
      console.warn(`⚠️ No adapter found for provider: ${providerId}`);
      return [];
    }

    try {
      const authConfig: AuthConfig = {
        type: 'bearer',
        ...(apiKey && { token: apiKey, apiKey: apiKey })
      };

      // Use existing adapter system
      const models = await this.discoverModels(providerId, authConfig);
      return models;
    } catch (error) {
      console.warn(`⚠️ Adapter-based fetch failed for ${providerId}:`, error);
      return [];
    }
  }

  /**
   * Batch fetch models for multiple providers
   */
  async fetchMultipleProviders(providerConfigs: Array<{providerId: string, apiKey?: string}>): Promise<Record<string, ModelInfo[]>> {
    const results: Record<string, ModelInfo[]> = {};

    // Fetch in parallel with some delay to avoid overwhelming APIs
    const promises = providerConfigs.map(async (config, index) => {
      // Stagger requests by 500ms each
      await new Promise(resolve => setTimeout(resolve, index * 500));

      try {
        const models = await this.fetchModels(config.providerId, config.apiKey);
        results[config.providerId] = models;
      } catch (error) {
        console.error(`Failed to fetch models for ${config.providerId}:`, error);
        results[config.providerId] = [];
      }
    });

    await Promise.allSettled(promises);
    return results;
  }

  /**
   * Check if proxy server is healthy
   */
  async checkProxyHealth(): Promise<boolean> {
    return this.proxyLauncher.checkProxyHealth();
  }

  /**
   * Get proxy status information
   */
  getProxyStatus(): { isRunning: boolean; url: string } {
    return {
      isRunning: this.proxyLauncher.isProxyRunning(),
      url: this.proxyLauncher.getProxyUrl()
    };
  }

  // Cache management
  private getFromCache(providerId: string, allowStale = false): ModelInfo[] | null {
    const entry = this.cache.get(providerId);
    if (!entry) return null;

    const isStale = Date.now() - entry.timestamp > this.CACHE_TTL;
    if (isStale && !allowStale) return null;

    return entry.data;
  }

  private setCache(providerId: string, models: ModelInfo[]): void {
    // Validate local models before caching
    const validatedModels = this.validateLocalModels(models, providerId);

    this.cache.set(providerId, {
      data: validatedModels,
      timestamp: Date.now()
    });
  }

  /**
   * Validate local models for compatibility (Cline-style validation)
   */
  private validateLocalModels(models: ModelInfo[], providerId: string): ModelInfo[] {
    if (providerId !== 'ollama' && providerId !== 'lmstudio') {
      return models; // Only validate local providers
    }

    return models.filter(model => {
      // Basic validation checks
      const hasValidId = model.id && model.id.trim().length > 0;
      const hasValidName = model.name && model.name.trim().length > 0;

      // For Ollama, check if model seems complete (not partial download)
      if (providerId === 'ollama') {
        const isComplete = !model.name.includes('pulling') && !model.name.includes('downloading');
        if (!isComplete) {
          console.log(`⚠️ Skipping incomplete Ollama model: ${model.name}`);
          return false;
        }
      }

      // For LM Studio, prefer GGUF models for better compatibility
      if (providerId === 'lmstudio') {
        const isGGUF = model.id.toLowerCase().includes('gguf') ||
                      (model as any).isGGUF === true;
        if (!isGGUF) {
          console.log(`⚠️ Non-GGUF model detected in LM Studio: ${model.name}`);
          // Don't filter out, but log warning
        }
      }

      return hasValidId && hasValidName;
    }).map(model => ({
      ...model,
      // Add validation metadata
      isValidated: true,
      validatedAt: new Date()
    }));
  }

  /**
   * Cleanup resources
   */
  shutdown(): void {
    this.proxyLauncher.shutdown();
    this.cache.clear();
    console.log('🔄 Enhanced API service shutdown');
  }
}

// Create singleton instance
export const enhancedApiService = new EnhancedApiService();
