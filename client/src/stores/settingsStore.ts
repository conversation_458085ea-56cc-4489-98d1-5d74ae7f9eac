import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { enhancedApiService } from '../services/api/enhancedApiService';

// Basic types for SahAI V2
export interface ModelInfo {
  id: string;
  name: string;
  description?: string;
  contextLength?: number;
  inputCost?: number;
  outputCost?: number;
  capabilities?: string[];
}

export interface ProviderConfig {
  id: string;
  name: string;
  type: string;
  isConfigured: boolean;
  isOnline: boolean;
  apiKey?: string;
  baseURL?: string;
  settings: {
    temperature: number;
    maxTokens: number;
    timeout: number;
    defaultModel?: string;
  };
}

interface SettingsState {
  // Provider management
  providers: ProviderConfig[];
  currentProvider: ProviderConfig | null;

  // Model management
  availableModels: ModelInfo[];
  currentModel: ModelInfo | null;
  modelsLoading: boolean;
  modelsError: string | null;

  // Enhanced API service status
  proxyStatus: { isRunning: boolean; url: string } | null;
  lastModelRefresh: Date | null;

  // Actions
  setCurrentProvider: (providerId: string | null) => Promise<void>;
  setCurrentModel: (modelId: string | null) => void;
  loadModelsForProvider: (providerId: string, forceRefresh?: boolean) => Promise<void>;
  updateProviderConfig: (id: string, updates: Partial<ProviderConfig>) => void;
  updateProviderApiKey: (providerId: string, apiKey: string) => Promise<void>;
  initializeDefaultProviders: () => void;

  // Enhanced methods
  initializeEnhancedApi: () => Promise<boolean>;
  refreshAllProviders: () => Promise<void>;
  checkProxyHealth: () => Promise<boolean>;
  getProxyStatus: () => { isRunning: boolean; url: string };
}

// Default providers for SahAI V2 - All 15 providers
const createDefaultProviders = (): ProviderConfig[] => {
  return [
    // Online Providers
    {
      id: 'openai',
      name: 'OpenAI',
      type: 'openai',
      isConfigured: false,
      isOnline: true,
      baseURL: 'https://api.openai.com/v1',
      settings: {
        temperature: 0.7,
        maxTokens: 4096,
        timeout: 30000,
      },
    },
    {
      id: 'anthropic',
      name: 'Anthropic',
      type: 'anthropic',
      isConfigured: false,
      isOnline: true,
      baseURL: 'https://api.anthropic.com',
      settings: {
        temperature: 0.7,
        maxTokens: 4096,
        timeout: 30000,
      },
    },
    {
      id: 'gemini',
      name: 'Google Gemini',
      type: 'gemini',
      isConfigured: false,
      isOnline: true,
      baseURL: 'https://generativelanguage.googleapis.com/v1',
      settings: {
        temperature: 0.7,
        maxTokens: 4096,
        timeout: 30000,
      },
    },
    {
      id: 'deepseek',
      name: 'DeepSeek',
      type: 'deepseek',
      isConfigured: false,
      isOnline: true,
      baseURL: 'https://api.deepseek.com',
      settings: {
        temperature: 0.7,
        maxTokens: 4096,
        timeout: 30000,
      },
    },
    {
      id: 'xai',
      name: 'xAI (Grok)',
      type: 'xai',
      isConfigured: false,
      isOnline: true,
      baseURL: 'https://api.x.ai/v1',
      settings: {
        temperature: 0.7,
        maxTokens: 4096,
        timeout: 30000,
      },
    },
    {
      id: 'mistral',
      name: 'Mistral AI',
      type: 'mistral',
      isConfigured: false,
      isOnline: true,
      baseURL: 'https://api.mistral.ai/v1',
      settings: {
        temperature: 0.7,
        maxTokens: 4096,
        timeout: 30000,
      },
    },
    {
      id: 'qwen',
      name: 'Qwen',
      type: 'qwen',
      isConfigured: false,
      isOnline: true,
      baseURL: 'https://dashscope.aliyuncs.com/api/v1',
      settings: {
        temperature: 0.7,
        maxTokens: 4096,
        timeout: 30000,
      },
    },
    {
      id: 'vertex',
      name: 'Google Vertex AI',
      type: 'vertex',
      isConfigured: false,
      isOnline: true,
      baseURL: 'https://us-central1-aiplatform.googleapis.com',
      settings: {
        temperature: 0.7,
        maxTokens: 4096,
        timeout: 30000,
      },
    },
    {
      id: 'openrouter',
      name: 'OpenRouter',
      type: 'openrouter',
      isConfigured: false,
      isOnline: true,
      baseURL: 'https://openrouter.ai/api/v1',
      settings: {
        temperature: 0.7,
        maxTokens: 4096,
        timeout: 30000,
      },
    },
    {
      id: 'perplexity',
      name: 'Perplexity',
      type: 'perplexity',
      isConfigured: false,
      isOnline: true,
      baseURL: 'https://api.perplexity.ai',
      settings: {
        temperature: 0.7,
        maxTokens: 4096,
        timeout: 30000,
      },
    },
    {
      id: 'together',
      name: 'Together AI',
      type: 'together',
      isConfigured: false,
      isOnline: true,
      baseURL: 'https://api.together.xyz/v1',
      settings: {
        temperature: 0.7,
        maxTokens: 4096,
        timeout: 30000,
      },
    },
    {
      id: 'groq',
      name: 'Groq',
      type: 'groq',
      isConfigured: false,
      isOnline: true,
      baseURL: 'https://api.groq.com/openai/v1',
      settings: {
        temperature: 0.7,
        maxTokens: 4096,
        timeout: 30000,
      },
    },
    {
      id: 'moonshot',
      name: 'Moonshot AI',
      type: 'moonshot',
      isConfigured: false,
      isOnline: true,
      baseURL: 'https://api.moonshot.cn/v1',
      settings: {
        temperature: 0.7,
        maxTokens: 4096,
        timeout: 30000,
      },
    },

    // Offline Providers (auto-configured since they don't need API keys)
    {
      id: 'ollama',
      name: 'Ollama',
      type: 'ollama',
      isConfigured: true, // Local providers are always "configured"
      isOnline: false,
      baseURL: 'http://localhost:11434',
      settings: {
        temperature: 0.7,
        maxTokens: 4096,
        timeout: 30000,
      },
    },
    {
      id: 'lmstudio',
      name: 'LM Studio',
      type: 'lmstudio',
      isConfigured: true, // Local providers are always "configured"
      isOnline: false,
      baseURL: 'http://localhost:1234/v1',
      settings: {
        temperature: 0.7,
        maxTokens: 4096,
        timeout: 30000,
      },
    },
  ];
};

// CEP-compatible storage implementation
const cepStorage = {
  getItem: (key: string): Promise<string | null> => {
    return new Promise((resolve) => {
      try {
        if (typeof window !== 'undefined' && (window as any).CSInterface) {
          const csInterface = new (window as any).CSInterface();
          csInterface.KVStorage.getItem(key, (data: string) => {
            resolve(data || null);
          });
        } else {
          // Fallback to localStorage for development
          resolve(localStorage.getItem(key));
        }
      } catch (error) {
        console.error('Error getting item from CEP storage:', error);
        resolve(null);
      }
    });
  },
  
  setItem: (key: string, value: string): Promise<void> => {
    return new Promise((resolve) => {
      try {
        if (typeof window !== 'undefined' && (window as any).CSInterface) {
          const csInterface = new (window as any).CSInterface();
          csInterface.KVStorage.setItem(key, value, () => {
            resolve();
          });
        } else {
          // Fallback to localStorage for development
          localStorage.setItem(key, value);
          resolve();
        }
      } catch (error) {
        console.error('Error setting item in CEP storage:', error);
        resolve();
      }
    });
  },
  
  removeItem: (key: string): Promise<void> => {
    return new Promise((resolve) => {
      try {
        if (typeof window !== 'undefined' && (window as any).CSInterface) {
          const csInterface = new (window as any).CSInterface();
          csInterface.KVStorage.removeItem(key, () => {
            resolve();
          });
        } else {
          // Fallback to localStorage for development
          localStorage.removeItem(key);
          resolve();
        }
      } catch (error) {
        console.error('Error removing item from CEP storage:', error);
        resolve();
      }
    });
  }
};

export const useSettingsStore = create<SettingsState>()(
  persist(
    (set, get) => ({
      // Initial state
      providers: [],
      currentProvider: null,
      availableModels: [],
      currentModel: null,
      modelsLoading: false,
      modelsError: null,
      proxyStatus: null,
      lastModelRefresh: null,

      // Initialize default providers
      initializeDefaultProviders: () => {
        const defaultProviders = createDefaultProviders();
        console.log('🔄 Initializing default providers:', defaultProviders.length);

        set((state) => ({
          providers: state.providers.length === 0 ? defaultProviders : state.providers,
        }));

        // Auto-select first provider if none selected
        const { currentProvider } = get();
        if (!currentProvider && defaultProviders.length > 0) {
          console.log('🔄 Auto-selecting first provider:', defaultProviders[0].name);
          // Don't await here to avoid blocking initialization
          get().setCurrentProvider(defaultProviders[0].id);
        }
      },

      // Set current provider and load its models
      setCurrentProvider: async (providerId: string | null) => {
        console.log('🔄 Setting current provider:', providerId);
        
        const { providers } = get();
        const provider = providerId ? providers.find(p => p.id === providerId) : null;
        
        if (provider && providerId) {
          set({ currentProvider: provider, currentModel: null });
          await get().loadModelsForProvider(providerId);
        } else {
          set({ currentProvider: null, currentModel: null, availableModels: [] });
        }
      },

      // Set current model
      setCurrentModel: (modelId: string | null) => {
        const { availableModels } = get();
        const model = modelId ? availableModels.find(m => m.id === modelId) : null;
        
        console.log('🔄 Setting current model:', modelId, model);
        set({ currentModel: model });
      },

      // Load models for a specific provider using enhanced API service
      loadModelsForProvider: async (providerId: string, forceRefresh = false) => {
        console.log(`🔄 Loading models for provider: ${providerId} (forceRefresh: ${forceRefresh})`);

        const state = get();
        set({ modelsLoading: true, modelsError: null });

        try {
          const provider = state.providers.find(p => p.id === providerId);
          if (!provider) {
            throw new Error(`Provider ${providerId} not found`);
          }

          // Use the enhanced API service with proxy support
          const models = await enhancedApiService.fetchModels(
            providerId,
            provider.apiKey,
            forceRefresh
          );

          set({
            availableModels: models,
            modelsLoading: false,
            modelsError: null,
            lastModelRefresh: new Date(),
            proxyStatus: enhancedApiService.getProxyStatus()
          });

          // Auto-select first model if none selected
          const { currentModel } = get();
          if (!currentModel && models.length > 0) {
            get().setCurrentModel(models[0].id);
          }

          console.log(`✅ Loaded ${models.length} models for ${providerId}`);

        } catch (error) {
          console.error(`❌ Error loading models for ${providerId}:`, error);
          set({
            modelsLoading: false,
            modelsError: error instanceof Error ? error.message : 'Failed to load models',
            availableModels: [],
            proxyStatus: enhancedApiService.getProxyStatus()
          });
        }
      },

      // Update provider configuration
      updateProviderConfig: (id: string, updates: Partial<ProviderConfig>) => {
        set((state) => ({
          providers: state.providers.map(provider =>
            provider.id === id ? { ...provider, ...updates } : provider
          ),
          currentProvider: state.currentProvider?.id === id 
            ? { ...state.currentProvider, ...updates }
            : state.currentProvider,
        }));
      },

      // Update provider API key
      updateProviderApiKey: async (providerId: string, apiKey: string) => {
        console.log('🔄 Updating API key for provider:', providerId);

        try {
          // Store API key securely in CEP storage
          await cepStorage.setItem(`sahai_api_key_${providerId}`, apiKey);

          // Update provider configuration
          // Local providers (ollama, lmstudio) are always configured regardless of API key
          const isLocalProvider = providerId === 'ollama' || providerId === 'lmstudio';
          get().updateProviderConfig(providerId, {
            apiKey,
            isConfigured: isLocalProvider ? true : !!apiKey
          });

        } catch (error) {
          console.error('Error updating API key:', error);
          throw error;
        }
      },

      // Initialize enhanced API service
      initializeEnhancedApi: async () => {
        console.log('🔄 Initializing enhanced API service...');
        try {
          const success = await enhancedApiService.initialize();
          set({ proxyStatus: enhancedApiService.getProxyStatus() });
          return success;
        } catch (error) {
          console.error('❌ Failed to initialize enhanced API service:', error);
          return false;
        }
      },

      // Refresh all configured providers in background
      refreshAllProviders: async () => {
        const state = get();
        const configuredProviders = state.providers.filter(p => p.isConfigured || p.id === 'openrouter');

        console.log(`🔄 Background refresh for ${configuredProviders.length} providers`);

        // Use batch fetching for efficiency
        const providerConfigs = configuredProviders.map(p => ({
          providerId: p.id,
          ...(p.apiKey && { apiKey: p.apiKey })
        }));

        try {
          const results = await enhancedApiService.fetchMultipleProviders(providerConfigs);

          // Update models for current provider if it was refreshed
          const currentProviderId = state.currentProvider?.id;
          if (currentProviderId && results[currentProviderId]) {
            set({
              availableModels: results[currentProviderId],
              modelsError: null,
              lastModelRefresh: new Date(),
              proxyStatus: enhancedApiService.getProxyStatus()
            });
            console.log(`✅ Background refresh updated ${currentProviderId} with ${results[currentProviderId].length} models`);
          }
        } catch (error) {
          console.error('❌ Background refresh failed:', error);
        }
      },

      // Check proxy health
      checkProxyHealth: async () => {
        const isHealthy = await enhancedApiService.checkProxyHealth();
        set({ proxyStatus: enhancedApiService.getProxyStatus() });
        return isHealthy;
      },

      // Get current proxy status
      getProxyStatus: () => {
        return enhancedApiService.getProxyStatus();
      },
    }),
    {
      name: 'sahai-settings-store',
      storage: cepStorage as any, // Type assertion to work around Zustand storage interface
      partialize: (state) => ({
        providers: state.providers,
        currentProvider: state.currentProvider,
        currentModel: state.currentModel,
        availableModels: [], // Reset models on restore
        modelsLoading: false, // Reset loading state
        modelsError: null, // Reset error state
        proxyStatus: null, // Reset proxy status on restore
        lastModelRefresh: null, // Reset refresh timestamp
      } as Partial<SettingsState>),
    }
  )
);
