# SahAI V2 – Next-Step Action Plan (No Full Code)

Below is a **concise punch-list** that you can tick off line-by-line.  
Each bullet contains:

* **File to touch**  
* **Exact snippet to paste** (≤ 6 lines)  
* **One-sentence rationale**

---

### 🔌 1. Adapter Registration (one-time setup)

| File | Snippet | Why |
|---|---|---|
| `src/stores/settingsStore.ts` | `enhancedApiService.registerAdapter(new AnthropicAdapter());` | Registers Anthropic models. |
|  | `enhancedApiService.registerAdapter(new OpenAIAdapter());` | Registers OpenAI models. |
|  | `enhancedApiService.registerAdapter(new GroqAdapter());` | Registers Groq. |
|  | …repeat for **12 remaining** providers. | Prevents `ADAPTER_NOT_FOUND`. |

---

### 🏁 2. Default Provider Auto-Select

| File | Snippet | Why |
|---|---|---|
| `src/stores/settingsStore.ts` → `initializeDefaultProviders()` | `if (providers.length && !get().currentProvider) setCurrentProvider(providers[0].id);` | Guarantees “Select Provider” text never stays idle.

---

### 🌐 3. Proxy Launch Guard

| File | Snippet | Why |
|---|---|---|
| `App.tsx` → `initializeApp()` | `const proxyOk = await initializeEnhancedApi(); if (!proxyOk) console.warn('⚠️ Proxy failed, using direct mode');` | CEP will still load even if proxy fails.

---

### 🔑 4. Vertex Provider Credentials Form

| File | Snippet | Why |
|---|---|---|
| `VertexProvider.tsx` inside `<div className="config-section">` (after region `<select>`) | `<input type="text" value={projectId} onChange={e => setProjectId(e.target.value)} placeholder="my-gcp-project" />` | Vertex requires `projectId`.

---

### 🛰️ 5. Ollama Health Ping

| File | Snippet | Why |
|---|---|---|
| `OllamaProvider.tsx` → `handleConnect()` | `const ok = await fetch(`${baseUrl}/api/tags`).then(r => r.ok);` | Verifies local server before listing models.

---

### 🧪 6. Quick Dev Validation

| File | Snippet | Why |
|---|---|---|
| CEP DevTools console | `await enhancedApiService.discoverModels('openai', { apiKey:'sk-test' })` | Confirms adapter + proxy combo works.

---

### ✅ 7. Sanity Checklist (no code)

- [ ] Extension loads in **all five** Adobe hosts.  
- [ ] Proxy PID logged in CEP console (`cep.process.createProcess`).  
- [ ] `TopBar` now shows provider name immediately.  
- [ ] `ModelSelector` dropdown populated with ≥1 model per provider.  

Once the above **seven micro-tasks** are complete, the Provider/Model flow behaves exactly like Cline—**inside CEP**.