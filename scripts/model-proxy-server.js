const express = require('express');
const fetch = require('node-fetch');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');

const app = express();
const PORT = 3131;

// Security and performance middleware
app.use(helmet());
app.use(compression());
app.use(express.json({ limit: '10mb' }));
app.use(cors({ origin: '*' }));

// Provider configurations with public endpoints
const PROVIDER_CONFIGS = {
  'openrouter': {
    publicEndpoint: 'https://openrouter.ai/api/v1/models',
    authEndpoint: 'https://openrouter.ai/api/v1/models',
    authHeader: 'Authorization',
    authPrefix: 'Bearer '
  },
  'together': {
    publicEndpoint: 'https://api.together.xyz/models/info',
    authEndpoint: 'https://api.together.xyz/v1/models',
    authHeader: 'Authorization',
    authPrefix: 'Bearer '
  },
  'groq': {
    publicEndpoint: 'https://api.groq.com/openai/v1/models',
    authEndpoint: 'https://api.groq.com/openai/v1/models',
    authHeader: 'Authorization',
    authPrefix: 'Bearer '
  },
  'anthropic': {
    authEndpoint: 'https://api.anthropic.com/v1/models',
    authHeader: 'x-api-key',
    authPrefix: ''
  },
  'openai': {
    authEndpoint: 'https://api.openai.com/v1/models',
    authHeader: 'Authorization',
    authPrefix: 'Bearer '
  },
  'gemini': {
    authEndpoint: 'https://generativelanguage.googleapis.com/v1/models',
    authHeader: 'x-goog-api-key',
    authPrefix: ''
  },
  'deepseek': {
    authEndpoint: 'https://api.deepseek.com/v1/models',
    authHeader: 'Authorization',
    authPrefix: 'Bearer '
  },
  'xai': {
    authEndpoint: 'https://api.x.ai/v1/models',
    authHeader: 'Authorization',
    authPrefix: 'Bearer '
  },
  'mistral': {
    authEndpoint: 'https://api.mistral.ai/v1/models',
    authHeader: 'Authorization',
    authPrefix: 'Bearer '
  },
  'qwen': {
    authEndpoint: 'https://dashscope.aliyuncs.com/api/v1/models',
    authHeader: 'Authorization',
    authPrefix: 'Bearer '
  },
  'vertex': {
    authEndpoint: 'https://us-central1-aiplatform.googleapis.com/v1/models',
    authHeader: 'Authorization',
    authPrefix: 'Bearer '
  },
  'perplexity': {
    authEndpoint: 'https://api.perplexity.ai/models',
    authHeader: 'Authorization',
    authPrefix: 'Bearer '
  },
  'moonshot': {
    authEndpoint: 'https://api.moonshot.cn/v1/models',
    authHeader: 'Authorization',
    authPrefix: 'Bearer '
  },
  'ollama': {
    authEndpoint: 'http://localhost:11434/api/tags',
    authHeader: '',
    authPrefix: ''
  },
  'lmstudio': {
    authEndpoint: 'http://localhost:1234/v1/models',
    authHeader: '',
    authPrefix: ''
  }
};

// In-memory cache with TTL
const modelCache = new Map();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

// Rate limiting
const rateLimits = new Map();
const RATE_LIMIT = 60; // requests per minute per provider

// Main endpoint for model fetching
app.post('/api/models/:providerId', async (req, res) => {
  const { providerId } = req.params;
  const { apiKey, forceRefresh = false } = req.body;

  try {
    // Check rate limits
    if (isRateLimited(providerId)) {
      return res.status(429).json({ 
        error: 'Rate limit exceeded', 
        retryAfter: 60 
      });
    }

    // Check cache first (unless force refresh)
    if (!forceRefresh) {
      const cached = getFromCache(providerId);
      if (cached) {
        console.log(`Cache hit for ${providerId}`);
        return res.json(cached);
      }
    }

    const config = PROVIDER_CONFIGS[providerId];
    if (!config) {
      return res.status(400).json({ 
        error: `Unsupported provider: ${providerId}` 
      });
    }

    let models = [];

    // Strategy 0: Special handling for local providers (Ollama, LM Studio)
    if (providerId === 'ollama' || providerId === 'lmstudio') {
      const serviceName = providerId === 'ollama' ? 'Ollama' : 'LM Studio';
      const isRunning = await checkLocalService(config.authEndpoint, serviceName);

      if (!isRunning) {
        console.log(`${serviceName} service not running - returning empty model list`);
        return res.json([]);
      }

      // For local services, proceed directly to fetch
      try {
        console.log(`Fetching local models from ${serviceName}`);
        const response = await fetch(config.authEndpoint, {
          timeout: 5000, // Shorter timeout for local services
          headers: {
            'User-Agent': 'SahAI-CEP-Extension/2.0',
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();
          models = normalizeModels(providerId, data);
          console.log(`${serviceName} fetch successful: ${models.length} models`);
          return res.json(models);
        }
      } catch (error) {
        console.warn(`${serviceName} fetch failed:`, error.message);
        return res.json([]);
      }
    }

    // Strategy 1: Try public endpoint first (like Cline)
    if (config.publicEndpoint) {
      try {
        console.log(`Attempting public fetch for ${providerId}`);
        const response = await fetch(config.publicEndpoint, {
          timeout: 10000,
          headers: {
            'User-Agent': 'SahAI-CEP-Extension/2.0'
          }
        });

        if (response.ok) {
          const data = await response.json();
          models = normalizeModels(providerId, data);
          console.log(`Public fetch successful for ${providerId}: ${models.length} models`);
        }
      } catch (error) {
        console.warn(`Public fetch failed for ${providerId}:`, error.message);
      }
    }

    // Strategy 2: Try authenticated endpoint if API key provided and public failed
    if (models.length === 0 && apiKey && config.authEndpoint) {
      try {
        console.log(`Attempting authenticated fetch for ${providerId}`);
        const headers = {
          'User-Agent': 'SahAI-CEP-Extension/2.0',
          'Content-Type': 'application/json'
        };
        headers[config.authHeader] = config.authPrefix + apiKey;

        const response = await fetch(config.authEndpoint, {
          timeout: 15000,
          headers
        });

        if (response.ok) {
          const data = await response.json();
          models = normalizeModels(providerId, data);
          console.log(`Authenticated fetch successful for ${providerId}: ${models.length} models`);
        } else {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      } catch (error) {
        console.error(`Authenticated fetch failed for ${providerId}:`, error.message);
        return res.status(500).json({ 
          error: `Authentication failed: ${error.message}` 
        });
      }
    }

    // Strategy 3: Return default models if all else fails
    if (models.length === 0) {
      models = getDefaultModels(providerId);
      console.log(`Using default models for ${providerId}: ${models.length} models`);
    }

    // Cache the results
    setCache(providerId, models);
    updateRateLimit(providerId);

    res.json({
      provider: providerId,
      models,
      cached: false,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error(`Error fetching models for ${providerId}:`, error);
    res.status(500).json({ 
      error: error.message,
      provider: providerId 
    });
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    cache: {
      size: modelCache.size,
      providers: Array.from(modelCache.keys())
    }
  });
});

// Utility functions
function normalizeModels(providerId, data) {
  // Provider-specific normalization logic
  switch (providerId) {
    case 'openrouter':
      return (data.data || []).map(model => ({
        id: model.id,
        name: model.name || model.id,
        description: model.description,
        contextLength: model.context_length,
        inputCost: model.pricing?.prompt,
        outputCost: model.pricing?.completion,
        provider: providerId,
        isAvailable: true,
        lastUpdated: new Date()
      }));
    
    case 'openai':
    case 'groq':
    case 'deepseek':
    case 'xai':
    case 'mistral':
    case 'perplexity':
    case 'moonshot':
      return (data.data || []).map(model => ({
        id: model.id,
        name: model.id,
        description: `${model.id} model`,
        contextLength: getContextLength(model.id),
        provider: providerId,
        isAvailable: true,
        lastUpdated: new Date()
      }));

    case 'together':
      return (data || []).map(model => ({
        id: model.name || model.id,
        name: model.display_name || model.name || model.id,
        description: model.description || `${model.name} model`,
        contextLength: model.context_length || 4096,
        provider: providerId,
        isAvailable: true,
        lastUpdated: new Date()
      }));

    case 'anthropic':
      return (data.data || []).map(model => ({
        id: model.id,
        name: model.display_name || model.id,
        description: model.description || `${model.id} model`,
        contextLength: getContextLength(model.id),
        provider: providerId,
        isAvailable: true,
        lastUpdated: new Date()
      }));

    case 'gemini':
      return (data.models || []).map(model => ({
        id: model.name.replace('models/', ''),
        name: model.displayName || model.name.replace('models/', ''),
        description: model.description || `${model.name} model`,
        contextLength: model.inputTokenLimit || 4096,
        provider: providerId,
        isAvailable: true,
        lastUpdated: new Date()
      }));

    case 'qwen':
      return (data.data || []).map(model => ({
        id: model.id,
        name: model.id,
        description: `${model.id} model`,
        contextLength: getContextLength(model.id),
        provider: providerId,
        isAvailable: true,
        lastUpdated: new Date()
      }));

    case 'vertex':
      return (data.models || []).map(model => ({
        id: model.name.replace('models/', ''),
        name: model.displayName || model.name.replace('models/', ''),
        description: model.description || `${model.name} model`,
        contextLength: model.inputTokenLimit || 4096,
        provider: providerId,
        isAvailable: true,
        lastUpdated: new Date()
      }));

    case 'ollama':
      return (data.models || []).map(model => ({
        id: model.name,
        name: model.name || model.model,
        description: model.details ? `${model.name} - ${model.details.parameter_size || 'Unknown size'}` : `Ollama ${model.name} model`,
        contextLength: model.details?.context_length || 4096,
        provider: providerId,
        isAvailable: true,
        lastUpdated: new Date(model.modified_at || Date.now()),
        // Additional Ollama-specific metadata
        size: model.size,
        digest: model.digest,
        family: model.details?.family,
        format: model.details?.format,
        parameterSize: model.details?.parameter_size,
        quantizationLevel: model.details?.quantization_level
      }));

    case 'lmstudio':
      return (data.data || []).map(model => ({
        id: model.id,
        name: model.id,
        description: model.description || `LM Studio ${model.id} model`,
        contextLength: model.context_length || 4096,
        provider: providerId,
        isAvailable: true,
        lastUpdated: new Date(),
        // Additional LM Studio-specific metadata
        object: model.object,
        owned_by: model.owned_by,
        // Check if it's a GGUF model
        isGGUF: model.id.toLowerCase().includes('gguf') || model.id.toLowerCase().includes('.gguf'),
        // Extract model family/type from ID
        modelFamily: extractModelFamily(model.id)
      }));

    default:
      return [];
  }
}

function getContextLength(modelId) {
  const contextMap = {
    'gpt-4': 8192,
    'gpt-4-turbo': 128000,
    'gpt-3.5-turbo': 4096,
    'claude-3-sonnet': 200000,
    'claude-3-haiku': 200000,
    'llama-3-70b': 8192
  };
  return contextMap[modelId] || 4096;
}

function getDefaultModels(providerId) {
  console.log(`❌ No models available for ${providerId} - API fetch failed`);
  return []; // Return empty array instead of hardcoded models
}

// Helper function to extract model family from LM Studio model ID
function extractModelFamily(modelId) {
  const id = modelId.toLowerCase();
  if (id.includes('llama')) return 'Llama';
  if (id.includes('mistral')) return 'Mistral';
  if (id.includes('codellama')) return 'Code Llama';
  if (id.includes('deepseek')) return 'DeepSeek';
  if (id.includes('qwen')) return 'Qwen';
  if (id.includes('phi')) return 'Phi';
  if (id.includes('gemma')) return 'Gemma';
  if (id.includes('claude')) return 'Claude';
  return 'Unknown';
}

// Enhanced local service connection checker
async function checkLocalService(url, serviceName) {
  try {
    const response = await fetch(url, {
      method: 'GET',
      timeout: 2000 // 2 second timeout for local services
    });
    if (response.ok) {
      console.log(`✅ ${serviceName} is running on ${url}`);
      return true;
    }
  } catch (error) {
    console.log(`❌ ${serviceName} not available on ${url}: ${error.message}`);
  }
  return false;
}

// Cache management
function getFromCache(providerId) {
  const entry = modelCache.get(providerId);
  if (entry && Date.now() - entry.timestamp < CACHE_TTL) {
    return entry.data;
  }
  return null;
}

function setCache(providerId, data) {
  modelCache.set(providerId, {
    data: { provider: providerId, models: data, cached: true, timestamp: new Date().toISOString() },
    timestamp: Date.now()
  });
}

// Rate limiting
function isRateLimited(providerId) {
  const limit = rateLimits.get(providerId);
  if (!limit) return false;
  
  const now = Date.now();
  if (now - limit.resetTime > 60000) {
    rateLimits.delete(providerId);
    return false;
  }
  
  return limit.count >= RATE_LIMIT;
}

function updateRateLimit(providerId) {
  const now = Date.now();
  const limit = rateLimits.get(providerId) || { count: 0, resetTime: now };
  
  if (now - limit.resetTime > 60000) {
    limit.count = 1;
    limit.resetTime = now;
  } else {
    limit.count++;
  }
  
  rateLimits.set(providerId, limit);
}

app.listen(PORT, () => {
  console.log(`🚀 SahAI Model Proxy Server running on port ${PORT}`);
  console.log(`📊 Supported providers: ${Object.keys(PROVIDER_CONFIGS).join(', ')}`);
});
