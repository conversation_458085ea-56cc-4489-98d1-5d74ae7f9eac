#!/usr/bin/env node

/**
 * Copy CEP Essential Files to Dist Directory
 * Copies CSXS/manifest.xml and extendscript files to the build output
 */

const fs = require('fs-extra');
const path = require('path');

// Configuration
const CONFIG = {
  sourceDir: path.join(__dirname, '..'),
  distDir: path.join(__dirname, '../dist'),
  filesToCopy: [
    {
      src: 'CSXS',
      dest: 'CSXS',
      description: 'CEP Manifest and Configuration'
    },
    {
      src: 'extendscript',
      dest: 'extendscript',
      description: 'ExtendScript Files'
    }
  ],
  // Additional files to copy after main build
  additionalCopies: [
    {
      src: 'client/index.html',
      dest: 'index.html',
      description: 'Main HTML File (move Vite output to root for CEP)'
    }
  ],
  // Static files to copy from source (not from dist)
  staticCopies: [
    {
      src: 'client/CSInterface.js',
      dest: 'CSInterface.js',
      description: 'Adobe CSInterface Library (preserved filename for CEP)'
    },
    {
      src: '.debug',
      dest: '.debug',
      description: 'CEP Debug Configuration (enables debug mode)'
    }
  ]
};

/**
 * Copy directory recursively
 */
function copyDirectory(src, dest) {
  // Create destination directory if it doesn't exist
  if (!fs.existsSync(dest)) {
    fs.mkdirSync(dest, { recursive: true });
  }

  // Files and patterns to exclude
  const excludePatterns = [
    /\.backup\./,           // Any backup files
    /manifest\.xml\.backup/, // Manifest backup files
    /\.DS_Store$/,          // macOS system files
    /Thumbs\.db$/,          // Windows system files
    /\.log$/                // Log files
  ];

  // Read source directory
  const entries = fs.readdirSync(src, { withFileTypes: true });

  for (const entry of entries) {
    // Check if file should be excluded
    const shouldExclude = excludePatterns.some(pattern => pattern.test(entry.name));
    if (shouldExclude) {
      console.log(`   ⏭️  Skipping excluded file: ${entry.name}`);
      continue;
    }

    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);

    if (entry.isDirectory()) {
      // Recursively copy subdirectory
      copyDirectory(srcPath, destPath);
    } else {
      // Copy file
      fs.copyFileSync(srcPath, destPath);
    }
  }
}

/**
 * Fix HTML file paths for CEP compatibility
 */
function fixHtmlPaths(htmlFilePath) {
  try {
    let htmlContent = fs.readFileSync(htmlFilePath, 'utf8');

    // Fix Vite-generated HTML for CEP compatibility
    htmlContent = htmlContent
      // Remove type="module" for CEP compatibility (Vite adds this)
      .replace(/type="module"\s+crossorigin/g, 'crossorigin')
      .replace(/type="module"/g, '')
      // Remove nomodule attributes that Vite might add
      .replace(/\s+nomodule/g, '')
      // Fix any remaining relative paths
      .replace(/href="\.\.\/assets\//g, 'href="./assets/')
      .replace(/src="\.\.\/assets\//g, 'src="./assets/');

    // Ensure CSInterface.js is loaded before the main bundle
    // Vite should have already included the main bundle, we just need to add CSInterface
    if (!htmlContent.includes('CSInterface.js')) {
      // Add CSInterface.js before the main script
      htmlContent = htmlContent.replace(
        /(<script[^>]*src="[^"]*assets\/[^"]*\.js"[^>]*><\/script>)/,
        '<script src="./CSInterface.js"></script>\n    $1'
      );
      console.log(`   ✅ Added CSInterface.js before main bundle`);
    } else {
      console.log(`   ✅ CSInterface.js already present`);
    }

    fs.writeFileSync(htmlFilePath, htmlContent, 'utf8');
    console.log(`   🔧 Fixed HTML file paths and removed ES modules for CEP compatibility`);

    // Log final script tags for debugging
    const finalScripts = htmlContent.match(/<script[^>]*>/g) || [];
    console.log(`   📄 Final script tags:`, finalScripts);

  } catch (error) {
    console.log(`   ⚠️  Could not fix HTML paths: ${error.message}`);
  }
}

/**
 * Validate build output
 */
function validateBuildOutput() {
  console.log('\n🔍 Validating build output...');

  const distFiles = [];

  function scanDirectory(dir, prefix = '') {
    const files = fs.readdirSync(dir, { withFileTypes: true });

    files.forEach(file => {
      const fullPath = path.join(dir, file.name);
      const relativePath = path.join(prefix, file.name);

      if (file.isDirectory()) {
        scanDirectory(fullPath, relativePath);
      } else {
        distFiles.push(relativePath);
      }
    });
  }

  scanDirectory(CONFIG.distDir);

  console.log('   📁 Files in dist directory:');
  distFiles.forEach(file => {
    console.log(`     - ${file}`);
  });

  // Check for critical files
  const criticalFiles = [
    'index.html',
    'CSInterface.js',
    'CSXS/manifest.xml'
  ];

  const missingFiles = criticalFiles.filter(file =>
    !distFiles.includes(file)
  );

  if (missingFiles.length > 0) {
    console.log(`   ❌ Missing critical files: ${missingFiles.join(', ')}`);
    return false;
  }

  // Check for JS files
  const jsFiles = distFiles.filter(file => file.endsWith('.js'));
  console.log(`   📄 JavaScript files found: ${jsFiles.join(', ')}`);

  if (jsFiles.length === 0) {
    console.log(`   ❌ No JavaScript files found in build output!`);
    return false;
  }

  return true;
}

/**
 * Get directory size for reporting
 */
function getDirectorySize(dirPath) {
  let totalSize = 0;
  
  function calculateSize(currentPath) {
    const stats = fs.statSync(currentPath);
    
    if (stats.isDirectory()) {
      const files = fs.readdirSync(currentPath);
      files.forEach(file => {
        calculateSize(path.join(currentPath, file));
      });
    } else {
      totalSize += stats.size;
    }
  }
  
  try {
    calculateSize(dirPath);
    return totalSize;
  } catch (error) {
    return 0;
  }
}

/**
 * Format file size for display
 */
function formatFileSize(bytes) {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Count files in directory
 */
function countFiles(dirPath) {
  let fileCount = 0;
  
  function count(currentPath) {
    const stats = fs.statSync(currentPath);
    
    if (stats.isDirectory()) {
      const files = fs.readdirSync(currentPath);
      files.forEach(file => {
        count(path.join(currentPath, file));
      });
    } else {
      fileCount++;
    }
  }
  
  try {
    count(dirPath);
    return fileCount;
  } catch (error) {
    return 0;
  }
}

/**
 * Main copy function
 */
async function copyCEPFiles() {
  console.log('📁 Copying CEP Essential Files to Dist Directory');
  console.log('=================================================\n');

  // Check if dist directory exists
  if (!fs.existsSync(CONFIG.distDir)) {
    console.error('❌ Dist directory not found:', CONFIG.distDir);
    console.error('   Please run the build process first.');
    process.exit(1);
  }

  let totalFilesCopied = 0;
  let totalSizeCopied = 0;

  // Copy each configured directory/file
  CONFIG.filesToCopy.forEach((item, index) => {
    const srcPath = path.join(CONFIG.sourceDir, item.src);
    const destPath = path.join(CONFIG.distDir, item.dest);

    console.log(`${index + 1}. Copying ${item.description}`);
    console.log(`   Source: ${srcPath}`);
    console.log(`   Destination: ${destPath}`);

    // Check if source exists
    if (!fs.existsSync(srcPath)) {
      console.log(`   ⚠️  Source not found, skipping...`);
      console.log('');
      return;
    }

    try {
      // Get source info before copying
      const sourceStats = fs.statSync(srcPath);
      
      if (sourceStats.isDirectory()) {
        const fileCount = countFiles(srcPath);
        const dirSize = getDirectorySize(srcPath);
        
        console.log(`   📊 Source: ${fileCount} files, ${formatFileSize(dirSize)}`);
        
        // Copy directory
        copyDirectory(srcPath, destPath);
        
        totalFilesCopied += fileCount;
        totalSizeCopied += dirSize;
        
        console.log(`   ✅ Directory copied successfully`);
      } else {
        // Copy single file
        const fileSize = sourceStats.size;
        
        // Ensure destination directory exists
        const destDir = path.dirname(destPath);
        if (!fs.existsSync(destDir)) {
          fs.mkdirSync(destDir, { recursive: true });
        }
        
        fs.copyFileSync(srcPath, destPath);
        
        totalFilesCopied += 1;
        totalSizeCopied += fileSize;
        
        console.log(`   ✅ File copied successfully (${formatFileSize(fileSize)})`);
      }
    } catch (error) {
      console.log(`   ❌ Failed to copy: ${error.message}`);
    }

    console.log('');
  });

  // Handle additional copies (files that need to be moved/copied after build)
  if (CONFIG.additionalCopies) {
    CONFIG.additionalCopies.forEach((item, index) => {
      const srcPath = path.join(CONFIG.distDir, item.src);
      const destPath = path.join(CONFIG.distDir, item.dest);

      console.log(`${CONFIG.filesToCopy.length + index + 1}. Copying ${item.description}`);
      console.log(`   Source: ${srcPath}`);
      console.log(`   Destination: ${destPath}`);

      // Check if source exists
      if (!fs.existsSync(srcPath)) {
        console.log(`   ⚠️  Source not found, skipping...`);
        console.log('');
        return;
      }

      try {
        // Get source info before copying
        const sourceStats = fs.statSync(srcPath);
        const fileSize = sourceStats.size;

        // Ensure destination directory exists
        const destDir = path.dirname(destPath);
        if (!fs.existsSync(destDir)) {
          fs.mkdirSync(destDir, { recursive: true });
        }

        fs.copyFileSync(srcPath, destPath);

        // Fix HTML file paths if this is the main HTML file
        if (item.dest === 'index.html') {
          fixHtmlPaths(destPath);
        }

        totalFilesCopied += 1;
        totalSizeCopied += fileSize;

        console.log(`   ✅ File copied successfully (${formatFileSize(fileSize)})`);
      } catch (error) {
        console.log(`   ❌ Failed to copy: ${error.message}`);
      }

      console.log('');
    });
  }

  // Handle static copies (files from source directory, not dist)
  if (CONFIG.staticCopies) {
    CONFIG.staticCopies.forEach((item, index) => {
      const srcPath = path.join(CONFIG.sourceDir, item.src);
      const destPath = path.join(CONFIG.distDir, item.dest);

      console.log(`${CONFIG.filesToCopy.length + (CONFIG.additionalCopies?.length || 0) + index + 1}. Copying ${item.description}`);
      console.log(`   Source: ${srcPath}`);
      console.log(`   Destination: ${destPath}`);

      // Check if source exists
      if (!fs.existsSync(srcPath)) {
        console.log(`   ⚠️  Source not found, skipping...`);
        console.log('');
        return;
      }

      try {
        // Get source info before copying
        const sourceStats = fs.statSync(srcPath);
        const fileSize = sourceStats.size;

        // Ensure destination directory exists
        const destDir = path.dirname(destPath);
        if (!fs.existsSync(destDir)) {
          fs.mkdirSync(destDir, { recursive: true });
        }

        fs.copyFileSync(srcPath, destPath);

        totalFilesCopied += 1;
        totalSizeCopied += fileSize;

        console.log(`   ✅ File copied successfully (${formatFileSize(fileSize)})`);
      } catch (error) {
        console.log(`   ❌ Failed to copy: ${error.message}`);
      }

      console.log('');
    });
  }

  // Process the Vite-generated HTML file (don't overwrite it)
  const htmlDestPath = path.join(CONFIG.distDir, 'index.html');

  console.log(`${CONFIG.filesToCopy.length + (CONFIG.staticCopies?.length || 0) + 1}. Processing Vite-Generated HTML File`);
  console.log(`   File: ${htmlDestPath}`);

  if (fs.existsSync(htmlDestPath)) {
    try {
      const sourceStats = fs.statSync(htmlDestPath);
      const fileSize = sourceStats.size;

      // Fix HTML file paths for CEP compatibility
      fixHtmlPaths(htmlDestPath);

      totalFilesCopied += 1;
      totalSizeCopied += fileSize;

      console.log(`   ✅ HTML file processed successfully (${formatFileSize(fileSize)})`);
    } catch (error) {
      console.log(`   ❌ Failed to process HTML: ${error.message}`);
    }
  } else {
    console.log(`   ⚠️  Vite-generated HTML not found, skipping...`);
  }
  console.log('');

  // Clean up redundant and Vite-processed files
  console.log('🧹 Cleaning up redundant files...');
  try {
    let cleanupCount = 0;

    // Remove backup files from dist directory
    function removeBackupFiles(dir) {
      if (!fs.existsSync(dir)) return;

      const files = fs.readdirSync(dir, { withFileTypes: true });
      files.forEach(file => {
        const filePath = path.join(dir, file.name);

        if (file.isDirectory()) {
          removeBackupFiles(filePath); // Recursive
        } else if (file.name.includes('.backup.') || file.name.startsWith('manifest.xml.backup')) {
          fs.unlinkSync(filePath);
          console.log(`   🗑️  Removed backup file: ${file.name}`);
          cleanupCount++;
        }
      });
    }

    removeBackupFiles(CONFIG.distDir);

    // Remove Vite-processed CSInterface.js files
    const assetsDir = path.join(CONFIG.distDir, 'assets');
    if (fs.existsSync(assetsDir)) {
      const files = fs.readdirSync(assetsDir);
      const viteCSFiles = files.filter(file => file.startsWith('CSInterface.') && file.endsWith('.js'));

      viteCSFiles.forEach(file => {
        const filePath = path.join(assetsDir, file);
        fs.unlinkSync(filePath);
        console.log(`   🗑️  Removed Vite-processed: ${file}`);
        cleanupCount++;
      });
    }

    // Remove client/index.html after copying to root, then remove client directory if empty
    const clientHtmlPath = path.join(CONFIG.distDir, 'client', 'index.html');
    if (fs.existsSync(clientHtmlPath)) {
      fs.unlinkSync(clientHtmlPath);
      console.log(`   🗑️  Removed duplicate: client/index.html`);
      cleanupCount++;
    }

    const clientDir = path.join(CONFIG.distDir, 'client');
    if (fs.existsSync(clientDir)) {
      const clientDirContents = fs.readdirSync(clientDir);
      if (clientDirContents.length === 0) {
        fs.rmdirSync(clientDir);
        console.log(`   🗑️  Removed empty directory: client/`);
        cleanupCount++;
      }
    }

    if (cleanupCount === 0) {
      console.log('   ✅ No redundant files to remove');
    }
  } catch (error) {
    console.log(`   ⚠️  Could not clean up files: ${error.message}`);
  }
  console.log('');

  // Summary
  console.log('📊 Copy Summary');
  console.log('================');
  console.log(`✅ Total files copied: ${totalFilesCopied}`);
  console.log(`📦 Total size copied: ${formatFileSize(totalSizeCopied)}`);
  console.log(`📁 Destination: ${CONFIG.distDir}`);

  // Verify critical files
  console.log('\n🔍 Verifying Critical CEP Files');
  console.log('=================================');
  
  const criticalFiles = [
    { path: 'CSXS/manifest.xml', description: 'CEP Manifest' },
    { path: 'index.html', description: 'Main HTML' },
    { path: 'extendscript', description: 'ExtendScript Directory' }
  ];

  let allCriticalFilesPresent = true;

  criticalFiles.forEach(file => {
    const filePath = path.join(CONFIG.distDir, file.path);
    const exists = fs.existsSync(filePath);
    
    console.log(`${exists ? '✅' : '❌'} ${file.description}: ${exists ? 'Present' : 'Missing'}`);
    
    if (!exists) {
      allCriticalFilesPresent = false;
    }
  });

  // Validate build output
  const isValid = validateBuildOutput();

  // Copy proxy server and dependencies
  console.log('\n7. Copying Proxy Server and Dependencies');
  console.log('   Source: scripts/');
  console.log('   Destination: dist/scripts/');

  try {
    await fs.copy(path.join(CONFIG.sourceDir, 'scripts'), path.join(CONFIG.distDir, 'scripts'));
    console.log('   ✅ Scripts directory copied successfully');

    // Copy essential node_modules for proxy server
    const proxyDeps = ['express', 'cors', 'node-fetch', 'helmet', 'compression'];
    const nodeModulesSource = path.join(CONFIG.sourceDir, 'node_modules');
    const nodeModulesDest = path.join(CONFIG.distDir, 'node_modules');

    await fs.ensureDir(nodeModulesDest);

    for (const dep of proxyDeps) {
      const depSource = path.join(nodeModulesSource, dep);
      const depDest = path.join(nodeModulesDest, dep);

      if (await fs.pathExists(depSource)) {
        await fs.copy(depSource, depDest);
        console.log(`   ✅ Copied dependency: ${dep}`);
      } else {
        console.log(`   ⚠️  Dependency not found: ${dep}`);
      }
    }

    // Copy package.json for proxy server
    await fs.copy(path.join(CONFIG.sourceDir, 'package.json'), path.join(CONFIG.distDir, 'package.json'));
    console.log('   ✅ Package.json copied successfully');

  } catch (error) {
    console.error('   ❌ Failed to copy proxy dependencies:', error.message);
  }

  if (allCriticalFilesPresent && isValid) {
    console.log('\n🎉 All critical CEP files are present in the dist directory!');
    console.log('   The extension is ready for installation and testing.');
    console.log('   🚀 Enhanced model loading with proxy server included!');
  } else {
    console.log('\n⚠️  Some critical files are missing or build validation failed.');
    console.log('   Please check the build process.');
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  copyCEPFiles().catch(console.error);
}

module.exports = { copyCEPFiles };
