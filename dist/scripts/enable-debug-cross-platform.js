#!/usr/bin/env node

/**
 * Cross-platform CEP debug enabler
 * Automatically detects the platform and runs the appropriate debug script
 */

const { spawn, exec } = require('child_process');
const path = require('path');
const os = require('os');

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

function colorLog(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function detectPlatform() {
  const platform = os.platform();
  switch (platform) {
    case 'darwin':
      return 'macos';
    case 'win32':
      return 'windows';
    case 'linux':
      return 'linux';
    default:
      return 'unknown';
  }
}

function runCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      ...options
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve(code);
      } else {
        reject(new Error(`Command failed with exit code ${code}`));
      }
    });

    child.on('error', (error) => {
      reject(error);
    });
  });
}

function checkCommandExists(command) {
  return new Promise((resolve) => {
    exec(`${command} --version`, (error) => {
      resolve(!error);
    });
  });
}

async function enableDebugMacOS() {
  colorLog('🍎 Detected macOS - Running shell script...', 'blue');
  
  const scriptPath = path.join(__dirname, 'enable-cep-debug.sh');
  
  try {
    // Make script executable
    await runCommand('chmod', ['+x', scriptPath]);
    
    // Run the script
    await runCommand('bash', [scriptPath]);
    
    colorLog('✅ macOS debug script completed successfully!', 'green');
  } catch (error) {
    colorLog(`❌ Failed to run macOS debug script: ${error.message}`, 'red');
    throw error;
  }
}

async function enableDebugWindows() {
  colorLog('🪟 Detected Windows - Choosing best available method...', 'blue');
  
  // Check for PowerShell first (preferred)
  const hasPowerShell = await checkCommandExists('powershell');
  
  if (hasPowerShell) {
    colorLog('🔧 Using PowerShell for advanced debugging features...', 'cyan');
    
    const scriptPath = path.join(__dirname, 'enable-cep-debug.ps1');
    
    try {
      await runCommand('powershell', [
        '-ExecutionPolicy', 'Bypass',
        '-File', scriptPath,
        '-Verbose'
      ]);
      
      colorLog('✅ PowerShell debug script completed successfully!', 'green');
    } catch (error) {
      colorLog(`⚠️  PowerShell script failed, falling back to batch file...`, 'yellow');
      await enableDebugWindowsBatch();
    }
  } else {
    colorLog('📝 PowerShell not available, using batch file...', 'yellow');
    await enableDebugWindowsBatch();
  }
}

async function enableDebugWindowsBatch() {
  const scriptPath = path.join(__dirname, 'enable-cep-debug.bat');
  
  try {
    await runCommand('cmd', ['/c', scriptPath]);
    colorLog('✅ Windows batch script completed successfully!', 'green');
  } catch (error) {
    colorLog(`❌ Failed to run Windows batch script: ${error.message}`, 'red');
    throw error;
  }
}

async function enableDebugLinux() {
  colorLog('🐧 Detected Linux', 'blue');
  colorLog('⚠️  CEP debugging is not directly supported on Linux', 'yellow');
  colorLog('', 'reset');
  colorLog('💡 For Linux development, consider:', 'cyan');
  console.log('   - Using Windows Subsystem for Linux (WSL) with Windows Adobe apps');
  console.log('   - Running Adobe apps in a Windows VM');
  console.log('   - Using Wine (limited support)');
  console.log('');
  colorLog('🔧 If you\'re using WSL, try:', 'cyan');
  console.log('   npm run enable-debug:windows');
  console.log('');
}

async function showHelp() {
  colorLog('🔧 SahAI CEP Extension - Cross-Platform Debug Enabler', 'green');
  console.log('');
  console.log('Usage:');
  console.log('  npm run enable-debug              # Auto-detect platform');
  console.log('  npm run enable-debug:mac          # Force macOS script');
  console.log('  npm run enable-debug:windows      # Force Windows batch');
  console.log('  npm run enable-debug:powershell   # Force PowerShell script');
  console.log('');
  console.log('Options:');
  console.log('  --help, -h                        # Show this help');
  console.log('  --platform <platform>             # Force specific platform');
  console.log('  --verbose, -v                     # Verbose output');
  console.log('');
  console.log('Platforms:');
  console.log('  macos, windows, linux');
  console.log('');
}

async function main() {
  const args = process.argv.slice(2);
  
  // Parse arguments
  let forcePlatform = null;
  let verbose = false;
  
  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    
    if (arg === '--help' || arg === '-h') {
      await showHelp();
      return;
    }
    
    if (arg === '--platform' && i + 1 < args.length) {
      forcePlatform = args[i + 1];
      i++; // Skip next argument
    }
    
    if (arg === '--verbose' || arg === '-v') {
      verbose = true;
    }
  }
  
  const platform = forcePlatform || detectPlatform();
  
  colorLog('🔧 SahAI CEP Extension - Cross-Platform Debug Enabler', 'green');
  console.log('');
  colorLog(`🖥️  Platform: ${platform}`, 'blue');
  
  if (verbose) {
    colorLog(`📊 OS Details: ${os.type()} ${os.release()} ${os.arch()}`, 'cyan');
    colorLog(`📁 Script Directory: ${__dirname}`, 'cyan');
  }
  
  console.log('');
  
  try {
    switch (platform) {
      case 'macos':
        await enableDebugMacOS();
        break;
        
      case 'windows':
        await enableDebugWindows();
        break;
        
      case 'linux':
        await enableDebugLinux();
        break;
        
      default:
        colorLog(`❌ Unsupported platform: ${platform}`, 'red');
        console.log('');
        colorLog('💡 Supported platforms: macos, windows, linux', 'cyan');
        console.log('');
        colorLog('🔧 You can force a specific platform with:', 'cyan');
        console.log('   node enable-debug-cross-platform.js --platform windows');
        process.exit(1);
    }
    
    // Show final instructions
    console.log('');
    colorLog('🎉 Debug enabler completed!', 'green');
    console.log('');
    colorLog('📝 Next steps:', 'yellow');
    console.log('   1. Restart all Adobe applications');
    console.log('   2. Install the extension: npm run install-extension');
    console.log('   3. Open the extension in your Adobe app');
    console.log('   4. Access debug console at the provided URLs');
    console.log('');
    
  } catch (error) {
    colorLog(`❌ Error: ${error.message}`, 'red');
    
    if (verbose) {
      console.error(error);
    }
    
    console.log('');
    colorLog('🔧 Troubleshooting:', 'cyan');
    console.log('   - Try running with administrator/sudo privileges');
    console.log('   - Check that Adobe applications are closed');
    console.log('   - Verify script permissions');
    console.log('   - Run with --verbose for more details');
    console.log('');
    
    process.exit(1);
  }
}

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  colorLog(`❌ Uncaught Exception: ${error.message}`, 'red');
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  colorLog(`❌ Unhandled Rejection: ${reason}`, 'red');
  process.exit(1);
});

// Run main function
if (require.main === module) {
  main();
}

module.exports = {
  detectPlatform,
  enableDebugMacOS,
  enableDebugWindows,
  enableDebugLinux,
  main
};
