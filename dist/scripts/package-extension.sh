#!/bin/bash

# SahAI CEP Extension - Package and Sign Extension Script
# This script builds, packages, and optionally signs the CEP extension

echo "📦 SahAI CEP Extension - Package and Sign"
echo "========================================="

# Configuration
EXTENSION_NAME="SahAI-CEP-Extension"
VERSION="1.0.0"
BUILD_DIR="./dist"
PACKAGE_DIR="./packages"
CERT_DIR="./certificates"
CERT_NAME="sahai-cep-dev"
TIMESTAMP_URL="http://timestamp.digicert.com"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to check if ZXPSignCmd is available
check_zxpsigncmd() {
    if command -v ZXPSignCmd &> /dev/null; then
        print_success "ZXPSignCmd found"
        return 0
    else
        print_warning "ZXPSignCmd not found in PATH"
        
        # Check common installation locations
        local common_paths=(
            "/Applications/Adobe/Adobe Exchange Panel/ZXPSignCmd"
            "/usr/local/bin/ZXPSignCmd"
            "./tools/ZXPSignCmd"
        )
        
        for path in "${common_paths[@]}"; do
            if [[ -f "$path" ]]; then
                print_success "Found ZXPSignCmd at: $path"
                export ZXPSIGNCMD="$path"
                return 0
            fi
        done
        
        print_error "ZXPSignCmd not found. Please install Adobe Extension Manager or download ZXPSignCmd"
        print_status "Download from: https://github.com/Adobe-CEP/CEP-Resources/tree/master/ZXPSignCmd"
        return 1
    fi
}

# Function to build the extension
build_extension() {
    print_status "Building extension..."
    
    if [[ ! -f "package.json" ]]; then
        print_error "package.json not found. Are you in the correct directory?"
        return 1
    fi
    
    # Run the build process
    npm run build
    
    if [[ $? -ne 0 ]]; then
        print_error "Build failed"
        return 1
    fi
    
    if [[ ! -d "$BUILD_DIR" ]]; then
        print_error "Build directory not found: $BUILD_DIR"
        return 1
    fi
    
    print_success "Extension built successfully"
    return 0
}

# Function to create unsigned ZXP package
create_unsigned_package() {
    print_status "Creating unsigned ZXP package..."
    
    # Create packages directory
    mkdir -p "$PACKAGE_DIR"
    
    local unsigned_package="$PACKAGE_DIR/${EXTENSION_NAME}-${VERSION}-unsigned.zxp"
    
    # Use ZXPSignCmd to create unsigned package
    local zxp_cmd="${ZXPSIGNCMD:-ZXPSignCmd}"
    
    "$zxp_cmd" -sign "$BUILD_DIR" "$unsigned_package" -p sahai123
    
    if [[ $? -eq 0 ]]; then
        print_success "Unsigned package created: $unsigned_package"
        return 0
    else
        print_error "Failed to create unsigned package"
        return 1
    fi
}

# Function to create signed ZXP package
create_signed_package() {
    print_status "Creating signed ZXP package..."
    
    local cert_file="$CERT_DIR/$CERT_NAME.p12"
    
    if [[ ! -f "$cert_file" ]]; then
        print_warning "Certificate not found: $cert_file"
        print_status "Run './scripts/create-self-signed-cert.sh' to create a development certificate"
        return 1
    fi
    
    # Create packages directory
    mkdir -p "$PACKAGE_DIR"
    
    local signed_package="$PACKAGE_DIR/${EXTENSION_NAME}-${VERSION}-signed.zxp"
    
    # Use ZXPSignCmd to create signed package
    local zxp_cmd="${ZXPSIGNCMD:-ZXPSignCmd}"
    
    "$zxp_cmd" -sign "$BUILD_DIR" "$signed_package" "$cert_file" sahai123 -tsa "$TIMESTAMP_URL"
    
    if [[ $? -eq 0 ]]; then
        print_success "Signed package created: $signed_package"
        return 0
    else
        print_error "Failed to create signed package"
        return 1
    fi
}

# Function to verify package
verify_package() {
    local package_file="$1"
    
    if [[ ! -f "$package_file" ]]; then
        print_error "Package file not found: $package_file"
        return 1
    fi
    
    print_status "Verifying package: $(basename "$package_file")"
    
    local zxp_cmd="${ZXPSIGNCMD:-ZXPSignCmd}"
    
    "$zxp_cmd" -verify "$package_file"
    
    if [[ $? -eq 0 ]]; then
        print_success "Package verification successful"
        return 0
    else
        print_warning "Package verification failed (this is expected for self-signed certificates)"
        return 1
    fi
}

# Function to create installation instructions
create_installation_instructions() {
    local instructions_file="$PACKAGE_DIR/INSTALLATION_INSTRUCTIONS.md"
    
    cat > "$instructions_file" << EOF
# SahAI CEP Extension Installation Instructions

## Prerequisites

1. **Enable Debug Mode** (Required for unsigned/self-signed extensions):
   - Run the debug mode setup script: \`./scripts/setup-debug-mode.sh\` (macOS) or \`./scripts/setup-debug-mode.bat\` (Windows)
   - This configures Adobe applications to allow unsigned extensions

## Installation Methods

### Method 1: Adobe Extension Manager (Recommended)
1. Download and install Adobe Extension Manager
2. Double-click the \`.zxp\` file to install
3. Restart Adobe After Effects

### Method 2: Manual Installation
1. Extract the \`.zxp\` file (it's a ZIP archive)
2. Copy the extracted folder to:
   - **macOS**: \`~/Library/Application Support/Adobe/CEP/extensions/\`
   - **Windows**: \`%APPDATA%\\Adobe\\CEP\\extensions\\\`
3. Restart Adobe After Effects

### Method 3: ZXPInstaller
1. Download ZXPInstaller from aescripts.com
2. Drag and drop the \`.zxp\` file onto ZXPInstaller
3. Restart Adobe After Effects

## Accessing the Extension

1. Open Adobe After Effects
2. Go to **Window > Extensions > SahAI Chat Bot**
3. The extension panel should appear

## Troubleshooting

If the extension doesn't appear:

1. **Check Debug Mode**: Ensure debug mode is properly configured
2. **Check Logs**: 
   - **macOS**: \`~/Library/Logs/CSXS/CEP12-AEFT.log\`
   - **Windows**: \`%APPDATA%\\Adobe\\CEP\\logs\\\`
3. **Verify Installation**: Check that the extension folder exists in the CEP extensions directory
4. **Restart**: Close and restart After Effects completely

## Support

For additional support, check the project documentation or create an issue in the project repository.
EOF

    print_success "Installation instructions created: $instructions_file"
}

# Function to display package information
display_package_info() {
    print_status "Package Information:"
    echo "===================="
    
    if [[ -d "$PACKAGE_DIR" ]]; then
        for package in "$PACKAGE_DIR"/*.zxp; do
            if [[ -f "$package" ]]; then
                local size=$(du -h "$package" | cut -f1)
                echo "📦 $(basename "$package") - $size"
            fi
        done
    fi
    
    echo ""
    print_status "Next Steps:"
    echo "1. Enable debug mode: ./scripts/setup-debug-mode.sh"
    echo "2. Install the extension using one of the methods in INSTALLATION_INSTRUCTIONS.md"
    echo "3. Restart Adobe After Effects"
    echo "4. Access the extension via Window > Extensions > SahAI Chat Bot"
}

# Main execution
main() {
    # Check if we're in the correct directory
    if [[ ! -f "package.json" ]] || [[ ! -d "CSXS" ]]; then
        print_error "Please run this script from the extension root directory"
        exit 1
    fi
    
    # Build the extension
    if ! build_extension; then
        exit 1
    fi
    
    # Check for ZXPSignCmd
    if ! check_zxpsigncmd; then
        print_warning "Skipping ZXP package creation due to missing ZXPSignCmd"
        print_status "You can manually create a ZIP archive of the dist folder"
        exit 1
    fi
    
    # Create unsigned package
    create_unsigned_package
    
    # Create signed package if certificate exists
    create_signed_package
    
    # Verify packages
    for package in "$PACKAGE_DIR"/*.zxp; do
        if [[ -f "$package" ]]; then
            verify_package "$package"
        fi
    done
    
    # Create installation instructions
    create_installation_instructions
    
    # Display package information
    display_package_info
    
    print_success "Extension packaging complete!"
}

# Run the main function
main "$@"
