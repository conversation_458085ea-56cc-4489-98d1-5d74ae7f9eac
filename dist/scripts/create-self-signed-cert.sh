#!/bin/bash

# SahAI CEP Extension - Self-Signed Certificate Creation Script
# This script creates a self-signed certificate for development purposes

echo "🔐 Creating Self-Signed Certificate for SahAI CEP Extension"
echo "==========================================================="

# Configuration
CERT_DIR="./certificates"
CERT_NAME="sahai-cep-dev"
CERT_SUBJECT="/C=US/ST=CA/L=San Francisco/O=SahAI/OU=Development/CN=SahAI CEP Extension"
CERT_DAYS=365

# Create certificates directory
if [[ ! -d "$CERT_DIR" ]]; then
    echo "📁 Creating certificates directory..."
    mkdir -p "$CERT_DIR"
fi

# Check if OpenSSL is available
if ! command -v openssl &> /dev/null; then
    echo "❌ OpenSSL is not installed. Please install OpenSSL first."
    echo "   On macOS: brew install openssl"
    echo "   On Ubuntu: sudo apt-get install openssl"
    exit 1
fi

echo "🔑 Generating private key..."
openssl genrsa -out "$CERT_DIR/$CERT_NAME.key" 2048

if [[ $? -ne 0 ]]; then
    echo "❌ Failed to generate private key"
    exit 1
fi

echo "📜 Creating certificate signing request..."
openssl req -new -key "$CERT_DIR/$CERT_NAME.key" -out "$CERT_DIR/$CERT_NAME.csr" -subj "$CERT_SUBJECT"

if [[ $? -ne 0 ]]; then
    echo "❌ Failed to create certificate signing request"
    exit 1
fi

echo "🔐 Generating self-signed certificate..."
openssl x509 -req -days $CERT_DAYS -in "$CERT_DIR/$CERT_NAME.csr" -signkey "$CERT_DIR/$CERT_NAME.key" -out "$CERT_DIR/$CERT_NAME.crt"

if [[ $? -ne 0 ]]; then
    echo "❌ Failed to generate self-signed certificate"
    exit 1
fi

echo "📦 Creating PKCS#12 certificate bundle..."
openssl pkcs12 -export -out "$CERT_DIR/$CERT_NAME.p12" -inkey "$CERT_DIR/$CERT_NAME.key" -in "$CERT_DIR/$CERT_NAME.crt" -password pass:sahai123

if [[ $? -ne 0 ]]; then
    echo "❌ Failed to create PKCS#12 certificate bundle"
    exit 1
fi

echo "🧹 Cleaning up temporary files..."
rm "$CERT_DIR/$CERT_NAME.csr"

echo "✅ Self-signed certificate created successfully!"
echo ""
echo "📋 Certificate Details:"
echo "   Private Key: $CERT_DIR/$CERT_NAME.key"
echo "   Certificate: $CERT_DIR/$CERT_NAME.crt"
echo "   PKCS#12 Bundle: $CERT_DIR/$CERT_NAME.p12"
echo "   Password: sahai123"
echo ""
echo "⚠️  Note: This is a self-signed certificate for development only."
echo "   For production, use a certificate from a trusted CA."
echo ""
echo "🔍 Certificate Information:"
openssl x509 -in "$CERT_DIR/$CERT_NAME.crt" -text -noout | grep -A 2 "Subject:"
openssl x509 -in "$CERT_DIR/$CERT_NAME.crt" -text -noout | grep -A 2 "Validity"
