#!/usr/bin/env node

/**
 * CSP Hash Generator for SahAI CEP Extension
 * Generates SHA-256 hashes for inline scripts and updates CSP accordingly
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// Configuration
const CONFIG = {
  htmlFile: path.join(__dirname, '../client/index.html'),
  outputFile: path.join(__dirname, '../dist/index.html'),
  backupFile: path.join(__dirname, '../client/index.html.backup'),
  encoding: 'utf8'
};

/**
 * Extract inline script content from HTML
 */
function extractInlineScripts(htmlContent) {
  const scripts = [];
  const scriptRegex = /<script(?![^>]*src=)([^>]*)>([\s\S]*?)<\/script>/gi;
  let match;

  while ((match = scriptRegex.exec(htmlContent)) !== null) {
    const attributes = match[1];
    const content = match[2].trim();
    
    // Skip empty scripts or scripts with src attribute
    if (content && !attributes.includes('src=')) {
      scripts.push({
        fullMatch: match[0],
        attributes: attributes,
        content: content,
        startIndex: match.index,
        endIndex: match.index + match[0].length
      });
    }
  }

  return scripts;
}

/**
 * Generate SHA-256 hash for script content
 */
function generateScriptHash(scriptContent) {
  const hash = crypto.createHash('sha256');
  hash.update(scriptContent, 'utf8');
  return 'sha256-' + hash.digest('base64');
}

/**
 * Update CSP with script hashes
 */
function updateCSP(htmlContent, scriptHashes) {
  // Find the existing CSP meta tag
  const cspRegex = /<meta\s+http-equiv=["']Content-Security-Policy["']\s+content=["']([\s\S]*?)["']\s*>/i;
  const match = cspRegex.exec(htmlContent);
  
  if (!match) {
    throw new Error('Content-Security-Policy meta tag not found');
  }

  const originalCSP = match[1];
  console.log('📋 Original CSP found');

  // Parse CSP directives
  const directives = originalCSP
    .split(';')
    .map(directive => directive.trim())
    .filter(directive => directive.length > 0);

  // Find and update script-src directive
  let scriptSrcIndex = -1;
  let scriptSrcDirective = '';

  for (let i = 0; i < directives.length; i++) {
    if (directives[i].startsWith('script-src')) {
      scriptSrcIndex = i;
      scriptSrcDirective = directives[i];
      break;
    }
  }

  if (scriptSrcIndex === -1) {
    throw new Error('script-src directive not found in CSP');
  }

  console.log('🔍 Found script-src directive:', scriptSrcDirective);

  // Remove 'unsafe-inline' and add hashes
  let newScriptSrc = scriptSrcDirective
    .replace(/'unsafe-inline'/g, '')
    .replace(/\s+/g, ' ')
    .trim();

  // Add script hashes
  const hashStrings = scriptHashes.map(hash => `'${hash}'`);
  newScriptSrc += ' ' + hashStrings.join(' ');

  // Clean up extra spaces
  newScriptSrc = newScriptSrc.replace(/\s+/g, ' ').trim();

  console.log('✨ New script-src directive:', newScriptSrc);

  // Update the directive
  directives[scriptSrcIndex] = newScriptSrc;

  // Reconstruct CSP
  const newCSP = directives.join(';\n        ') + ';';

  // Replace in HTML
  const newCspTag = match[0].replace(originalCSP, newCSP);
  const updatedHtml = htmlContent.replace(match[0], newCspTag);

  return updatedHtml;
}

/**
 * Create backup of original file
 */
function createBackup() {
  if (fs.existsSync(CONFIG.htmlFile)) {
    fs.copyFileSync(CONFIG.htmlFile, CONFIG.backupFile);
    console.log('💾 Backup created:', CONFIG.backupFile);
  }
}

/**
 * Restore from backup
 */
function restoreBackup() {
  if (fs.existsSync(CONFIG.backupFile)) {
    fs.copyFileSync(CONFIG.backupFile, CONFIG.htmlFile);
    console.log('🔄 Restored from backup');
  }
}

/**
 * Main function
 */
function generateCSPHashes() {
  console.log('🔐 CSP Hash Generator for SahAI CEP Extension');
  console.log('================================================\n');

  try {
    // Check if HTML file exists
    if (!fs.existsSync(CONFIG.htmlFile)) {
      throw new Error(`HTML file not found: ${CONFIG.htmlFile}`);
    }

    // Create backup
    createBackup();

    // Read HTML file
    console.log('📖 Reading HTML file:', CONFIG.htmlFile);
    const htmlContent = fs.readFileSync(CONFIG.htmlFile, CONFIG.encoding);

    // Extract inline scripts
    console.log('🔍 Extracting inline scripts...');
    const inlineScripts = extractInlineScripts(htmlContent);
    
    if (inlineScripts.length === 0) {
      console.log('ℹ️  No inline scripts found');
      return;
    }

    console.log(`📜 Found ${inlineScripts.length} inline script(s)`);

    // Generate hashes for each script
    const scriptHashes = [];
    inlineScripts.forEach((script, index) => {
      const hash = generateScriptHash(script.content);
      scriptHashes.push(hash);
      
      console.log(`🔑 Script ${index + 1} hash: ${hash}`);
      console.log(`   Content preview: ${script.content.substring(0, 100)}...`);
    });

    // Update CSP
    console.log('\n🔧 Updating Content Security Policy...');
    const updatedHtml = updateCSP(htmlContent, scriptHashes);

    // Write updated HTML
    const outputPath = CONFIG.outputFile || CONFIG.htmlFile;
    
    // Ensure output directory exists
    const outputDir = path.dirname(outputPath);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    fs.writeFileSync(outputPath, updatedHtml, CONFIG.encoding);
    console.log('✅ Updated HTML written to:', outputPath);

    // Summary
    console.log('\n📊 Summary:');
    console.log(`   • Processed ${inlineScripts.length} inline script(s)`);
    console.log(`   • Generated ${scriptHashes.length} CSP hash(es)`);
    console.log(`   • Removed 'unsafe-inline' from script-src`);
    console.log(`   • Updated CSP with script hashes`);
    console.log('\n🎉 CSP hash optimization completed successfully!');

    return {
      success: true,
      scriptCount: inlineScripts.length,
      hashes: scriptHashes,
      outputFile: outputPath
    };

  } catch (error) {
    console.error('❌ Error generating CSP hashes:', error.message);
    
    // Restore backup on error
    if (fs.existsSync(CONFIG.backupFile)) {
      console.log('🔄 Restoring backup due to error...');
      restoreBackup();
    }

    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * CLI interface
 */
function main() {
  const args = process.argv.slice(2);
  
  // Handle command line arguments
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
CSP Hash Generator for SahAI CEP Extension

Usage:
  node generate-csp-hash.js [options]

Options:
  --help, -h          Show this help message
  --restore           Restore from backup
  --output <path>     Specify output file path
  --dry-run           Show what would be done without making changes

Examples:
  node generate-csp-hash.js
  node generate-csp-hash.js --output ./dist/index.html
  node generate-csp-hash.js --restore
    `);
    return;
  }

  if (args.includes('--restore')) {
    restoreBackup();
    return;
  }

  // Set output file if specified
  const outputIndex = args.indexOf('--output');
  if (outputIndex !== -1 && args[outputIndex + 1]) {
    CONFIG.outputFile = path.resolve(args[outputIndex + 1]);
  }

  // Dry run mode
  if (args.includes('--dry-run')) {
    console.log('🔍 Dry run mode - no files will be modified');
    // TODO: Implement dry run logic
    return;
  }

  // Run the generator
  const result = generateCSPHashes();
  
  // Exit with appropriate code
  process.exit(result.success ? 0 : 1);
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = {
  generateCSPHashes,
  extractInlineScripts,
  generateScriptHash,
  updateCSP
};
