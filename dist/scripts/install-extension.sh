#!/bin/bash

# Install SahAI CEP Extension to Adobe CEP extensions directory

EXTENSION_NAME="sahai-cep-extension"
CEP_DIR="$HOME/Library/Application Support/Adobe/CEP/extensions"
SOURCE_DIR="./dist/sahai-cep-extension"
TARGET_DIR="$CEP_DIR/$EXTENSION_NAME"

echo "🚀 Installing SahAI CEP Extension..."

# Check if source directory exists
if [ ! -d "$SOURCE_DIR" ]; then
    echo "❌ Source directory not found: $SOURCE_DIR"
    echo "Please run 'npm run build && npm run package' first"
    exit 1
fi

# Create CEP extensions directory if it doesn't exist
mkdir -p "$CEP_DIR"

# Remove existing extension if it exists
if [ -d "$TARGET_DIR" ]; then
    echo "🗑️  Removing existing extension..."
    rm -rf "$TARGET_DIR"
fi

# Copy extension to CEP directory
echo "📋 Copying extension files..."
cp -r "$SOURCE_DIR" "$TARGET_DIR"

# Verify installation
if [ -d "$TARGET_DIR" ]; then
    echo "✅ Extension installed successfully!"
    echo "📁 Location: $TARGET_DIR"
    echo ""
    echo "📝 Next steps:"
    echo "1. Restart Adobe applications"
    echo "2. Go to Window > Extensions > SahAI Chat Bot"
    echo "3. If the extension doesn't appear, check CEP debugging is enabled"
    echo ""
    echo "🔧 To enable CEP debugging, run:"
    echo "   ./scripts/enable-cep-debug.sh"
else
    echo "❌ Installation failed!"
    exit 1
fi