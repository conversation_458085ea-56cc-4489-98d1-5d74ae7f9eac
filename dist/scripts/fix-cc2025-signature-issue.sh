#!/bin/bash

# Sah<PERSON>I CEP Extension - CC 2025 Signature Verification Fix
# This script specifically addresses signature verification issues in Adobe CC 2025

echo "🔧 Fixing Adobe CC 2025 Signature Verification Issue"
echo "===================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to check if running on macOS
check_macos() {
    if [[ "$OSTYPE" != "darwin"* ]]; then
        print_error "This script is designed for macOS"
        exit 1
    fi
}

# Function to detect CC 2025 CSXS version
detect_cc2025_csxs_version() {
    print_status "Detecting Adobe CC 2025 CSXS version..."
    
    # Check After Effects 2025 for CSXS version
    local ae_path="/Applications/Adobe After Effects 2025/Adobe After Effects 2025.app"
    
    if [[ -d "$ae_path" ]]; then
        # Look for CSXS version in the app bundle
        local csxs_versions=(14 13 12 11)
        
        for version in "${csxs_versions[@]}"; do
            # Check if this CSXS version exists in the system
            local pref_file="$HOME/Library/Preferences/com.adobe.CSXS.${version}.plist"
            local log_file="$HOME/Library/Logs/CSXS/CEP${version}-AEFT.log"
            
            if [[ -f "$log_file" ]] || [[ -f "$pref_file" ]]; then
                print_success "Found CSXS version $version for CC 2025"
                echo "$version"
                return 0
            fi
        done
        
        # Default to version 14 for CC 2025
        print_warning "Could not detect exact CSXS version, defaulting to 14 for CC 2025"
        echo "14"
        return 0
    else
        print_error "After Effects 2025 not found at expected path"
        return 1
    fi
}

# Function to configure debug mode with enhanced settings for CC 2025
configure_enhanced_debug_mode() {
    local version="$1"
    local pref_file="$HOME/Library/Preferences/com.adobe.CSXS.${version}.plist"
    
    print_status "Configuring enhanced debug mode for CSXS $version..."
    
    # Backup existing file
    if [[ -f "$pref_file" ]]; then
        local backup_file="${pref_file}.backup.$(date +%Y%m%d_%H%M%S)"
        cp "$pref_file" "$backup_file"
        print_status "Backed up existing preferences to: $backup_file"
    fi
    
    # Set comprehensive debug mode properties
    /usr/libexec/PlistBuddy -c "Set :PlayerDebugMode 1" "$pref_file" 2>/dev/null || \
    /usr/libexec/PlistBuddy -c "Add :PlayerDebugMode integer 1" "$pref_file"
    
    /usr/libexec/PlistBuddy -c "Set :LogLevel 6" "$pref_file" 2>/dev/null || \
    /usr/libexec/PlistBuddy -c "Add :LogLevel integer 6" "$pref_file"
    
    # Add signature bypass settings
    /usr/libexec/PlistBuddy -c "Set :SignatureBypass 1" "$pref_file" 2>/dev/null || \
    /usr/libexec/PlistBuddy -c "Add :SignatureBypass integer 1" "$pref_file"
    
    /usr/libexec/PlistBuddy -c "Set :ExtensionDebugging 1" "$pref_file" 2>/dev/null || \
    /usr/libexec/PlistBuddy -c "Add :ExtensionDebugging integer 1" "$pref_file"
    
    # Enhanced CEF command line parameters for CC 2025
    /usr/libexec/PlistBuddy -c "Delete :CEFCommandLine" "$pref_file" 2>/dev/null
    /usr/libexec/PlistBuddy -c "Add :CEFCommandLine array" "$pref_file"
    
    local cef_params=(
        "--allow-file-access-from-files"
        "--allow-file-access"
        "--enable-nodejs"
        "--mixed-context"
        "--disable-web-security"
        "--disable-features=VizDisplayCompositor"
        "--allow-running-insecure-content"
        "--ignore-certificate-errors"
        "--ignore-ssl-errors"
        "--ignore-certificate-errors-spki-list"
        "--disable-extensions-file-access-check"
    )
    
    for i in "${!cef_params[@]}"; do
        /usr/libexec/PlistBuddy -c "Add :CEFCommandLine:$i string ${cef_params[$i]}" "$pref_file" 2>/dev/null
    done
    
    print_success "Enhanced debug mode configured for CSXS $version"
}

# Function to create a .debug file with specific settings for CC 2025
create_debug_file() {
    local debug_file="./CSXS/.debug"
    
    print_status "Creating enhanced .debug file for CC 2025..."
    
    cat > "$debug_file" << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<ExtensionList>
    <Extension Id="com.sahai.cep.panel">
        <HostList>
            <Host Name="AEFT" Port="8001"/>
            <Host Name="PPRO" Port="8002"/>
            <Host Name="PHXS" Port="8003"/>
            <Host Name="ILST" Port="8004"/>
        </HostList>
    </Extension>
</ExtensionList>
EOF
    
    print_success "Enhanced .debug file created"
}

# Function to update manifest.xml for better CC 2025 compatibility
update_manifest_for_cc2025() {
    local manifest_file="./CSXS/manifest.xml"
    
    if [[ ! -f "$manifest_file" ]]; then
        print_error "Manifest file not found: $manifest_file"
        return 1
    fi
    
    print_status "Updating manifest.xml for CC 2025 compatibility..."
    
    # Create backup
    cp "$manifest_file" "${manifest_file}.backup.$(date +%Y%m%d_%H%M%S)"
    
    # Update CSXS version to support newer versions
    sed -i '' 's/<RequiredRuntime Name="CSXS" Version="[^"]*"\/>/<RequiredRuntime Name="CSXS" Version="11.0"\/>/g' "$manifest_file"
    
    # Ensure host version range includes CC 2025
    sed -i '' 's/\[18\.0,99\.9\]/[18.0,99.9]/g' "$manifest_file"
    
    print_success "Manifest updated for CC 2025 compatibility"
}

# Function to install extension directly to bypass signature issues
install_extension_directly() {
    print_status "Installing extension directly to CEP extensions directory..."
    
    # Build the extension first
    if [[ -f "package.json" ]]; then
        print_status "Building extension..."
        npm run build
        
        if [[ $? -ne 0 ]]; then
            print_error "Build failed"
            return 1
        fi
    else
        print_error "package.json not found. Are you in the correct directory?"
        return 1
    fi
    
    # Create extensions directory if it doesn't exist
    local ext_dir="$HOME/Library/Application Support/Adobe/CEP/extensions"
    local target_dir="$ext_dir/com.sahai.cep"
    
    mkdir -p "$ext_dir"
    
    # Remove existing installation
    if [[ -d "$target_dir" ]]; then
        print_status "Removing existing installation..."
        rm -rf "$target_dir"
    fi
    
    # Copy built extension
    print_status "Copying extension files..."
    cp -r "./dist" "$target_dir"
    
    if [[ $? -eq 0 ]]; then
        print_success "Extension installed directly to: $target_dir"
        return 0
    else
        print_error "Failed to install extension"
        return 1
    fi
}

# Function to verify the fix
verify_fix() {
    print_status "Verifying the signature verification fix..."
    
    # Check if extension is installed
    local target_dir="$HOME/Library/Application Support/Adobe/CEP/extensions/com.sahai.cep"
    
    if [[ -d "$target_dir" ]]; then
        print_success "Extension is installed at: $target_dir"
        
        # Check key files
        if [[ -f "$target_dir/CSXS/manifest.xml" ]]; then
            print_success "Manifest file found"
        else
            print_error "Manifest file missing"
        fi
        
        if [[ -f "$target_dir/index.html" ]]; then
            print_success "Main HTML file found"
        else
            print_error "Main HTML file missing"
        fi
    else
        print_error "Extension not found in CEP extensions directory"
    fi
    
    # Check debug mode configuration
    local csxs_version=$(detect_cc2025_csxs_version)
    local pref_file="$HOME/Library/Preferences/com.adobe.CSXS.${csxs_version}.plist"
    
    if [[ -f "$pref_file" ]]; then
        local debug_mode=$(/usr/libexec/PlistBuddy -c "Print :PlayerDebugMode" "$pref_file" 2>/dev/null)
        local signature_bypass=$(/usr/libexec/PlistBuddy -c "Print :SignatureBypass" "$pref_file" 2>/dev/null)
        
        print_status "Debug configuration for CSXS $csxs_version:"
        echo "   PlayerDebugMode: $debug_mode"
        echo "   SignatureBypass: $signature_bypass"
        
        if [[ "$debug_mode" == "1" ]] && [[ "$signature_bypass" == "1" ]]; then
            print_success "Debug mode and signature bypass are properly configured"
        else
            print_warning "Debug configuration may need adjustment"
        fi
    fi
}

# Main execution
main() {
    check_macos
    
    print_status "Starting CC 2025 signature verification fix..."
    echo ""
    
    # Detect CSXS version for CC 2025
    local csxs_version=$(detect_cc2025_csxs_version)
    
    if [[ -z "$csxs_version" ]]; then
        print_error "Could not detect CSXS version for CC 2025"
        exit 1
    fi
    
    echo ""
    print_status "Configuring debug mode for CSXS version $csxs_version..."
    configure_enhanced_debug_mode "$csxs_version"
    
    echo ""
    print_status "Creating enhanced debug configuration..."
    create_debug_file
    
    echo ""
    print_status "Updating manifest for CC 2025..."
    update_manifest_for_cc2025
    
    echo ""
    print_status "Installing extension directly..."
    install_extension_directly
    
    echo ""
    verify_fix
    
    echo ""
    print_success "CC 2025 signature verification fix complete!"
    echo ""
    print_status "Next steps:"
    echo "1. Close Adobe After Effects 2025 if it's running"
    echo "2. Restart Adobe After Effects 2025"
    echo "3. Look for 'SahAI Chat Bot' in Window > Extensions menu"
    echo ""
    print_status "If the extension still doesn't load, run:"
    echo "   ./scripts/diagnose-cep-issues.sh monitor"
    echo "   Then start After Effects to see real-time log output"
}

# Run the main function
main "$@"
