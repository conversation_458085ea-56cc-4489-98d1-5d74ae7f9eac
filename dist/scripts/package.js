const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Package script for SahAI CEP Extension
console.log('📦 Packaging SahAI CEP Extension...');

const projectRoot = path.resolve(__dirname, '..');
const buildDir = path.join(projectRoot, 'dist'); // Built files from Vite/Webpack
const packageDir = path.join(projectRoot, 'dist', 'sahai-cep-extension');

// Check if build exists
if (!fs.existsSync(buildDir) || !fs.existsSync(path.join(buildDir, 'index.html'))) {
    console.error('❌ Build not found. Please run "npm run build" first.');
    process.exit(1);
}

// Create package directory
if (fs.existsSync(packageDir)) {
    fs.rmSync(packageDir, { recursive: true, force: true });
}
fs.mkdirSync(packageDir, { recursive: true });

// Copy built files from dist to package
console.log('📋 Copying built application files...');
const builtFiles = [
    'index.html',           // Main HTML file
    'CSInterface.js',       // Adobe CEP library
    '.debug',              // Debug configuration
    'js',                  // JavaScript files (if any)
    'css',                 // CSS files (if any)
    'assets',              // Vite build assets
    'images',              // Image assets
    'icons',               // Extension icons
    'fonts'                // Font files
];

for (const file of builtFiles) {
    const srcPath = path.join(buildDir, file);
    const destPath = path.join(packageDir, file);
    if (fs.existsSync(srcPath)) {
        if (fs.statSync(srcPath).isDirectory()) {
            copyDirectory(srcPath, destPath);
            console.log(`✅ Copied directory: ${file}`);
        } else {
            fs.copyFileSync(srcPath, destPath);
            console.log(`✅ Copied file: ${file}`);
        }
    } else {
        console.log(`⚠️  File not found, skipping: ${file}`);
    }
}

// Copy CEP framework files
console.log('📋 Copying CEP framework files...');
copyDirectory(path.join(buildDir, 'CSXS'), path.join(packageDir, 'CSXS'));

// Copy ExtendScript files
console.log('📋 Copying ExtendScript files...');
copyDirectory(path.join(buildDir, 'extendscript'), path.join(packageDir, 'extendscript'));

// Copy public assets
console.log('📋 Copying public assets...');
copyDirectory(path.join(buildDir, 'public'), path.join(packageDir, 'public'));

// Copy documentation
console.log('📋 Copying documentation...');
const docFiles = ['README.md', 'TROUBLESHOOTING.md'];
for (const docFile of docFiles) {
    const srcPath = path.join(projectRoot, docFile);
    if (fs.existsSync(srcPath)) {
        fs.copyFileSync(srcPath, path.join(packageDir, docFile));
    }
}

// Create ZXP package if ZXPSignCmd is available
try {
    console.log('📦 Creating ZXP package...');
    const zxpPath = path.join(distDir, 'sahai-cep-extension.zxp');

    // Note: This requires ZXPSignCmd to be installed
    // For development, we'll just create the folder structure
    console.log('✅ Extension packaged successfully!');
    console.log(`📁 Package location: ${packageDir}`);
    console.log('💡 To create a ZXP file, use Adobe\'s ZXPSignCmd tool');
} catch (error) {
    console.log('⚠️  ZXP creation skipped (ZXPSignCmd not found)');
    console.log('✅ Extension folder created successfully!');
    console.log(`📁 Package location: ${packageDir}`);
}

function copyDirectory(src, dest) {
    if (!fs.existsSync(src)) {
        console.warn(`⚠️  Source directory not found: ${src}`);
        return;
    }

    fs.mkdirSync(dest, { recursive: true });

    const entries = fs.readdirSync(src, { withFileTypes: true });

    for (const entry of entries) {
        const srcPath = path.join(src, entry.name);
        const destPath = path.join(dest, entry.name);

        if (entry.isDirectory()) {
            copyDirectory(srcPath, destPath);
        } else {
            fs.copyFileSync(srcPath, destPath);
        }
    }
}